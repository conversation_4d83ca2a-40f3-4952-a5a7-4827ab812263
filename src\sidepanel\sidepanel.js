/**
 * @file AI Side Panel 侧边栏主脚本
 * @description 处理侧边栏的主要逻辑，包括界面初始化、用户交互、AI分析展示等
 */

// #region 全局变量
let aisp_currentLanguage = 'zh_CN';
let aisp_isAnalyzing = false;
let aisp_currentPageData = null;
let aisp_chatInterface = null;
let aisp_apiManager = null;
let aisp_configManager = null;
let aisp_isInitialized = false;
let aisp_messageHistory = [];
let aisp_analysisCache = new Map();
let aisp_replyGenerator = null;
let aisp_replySuggestions = null;
let aisp_templateManager = null;
let aisp_templatePopup = null;
let aisp_googleDriveAPI = null;
let aisp_knowledgeBase = null;
let aisp_cacheManager = null;
let aisp_performanceOptimizer = null;
let aisp_testFramework = null;
let aisp_performanceMonitor = null;
// #endregion

// #region 初始化
/**
 * @function aisp_initializeSidePanel - 初始化侧边栏
 * @description 设置事件监听器，加载配置，初始化界面
 */
async function aisp_initializeSidePanel() {
    if (aisp_isInitialized) return;

    try {
        console.log('AI Side Panel 侧边栏初始化中...');

        // 初始化多语言支持
        await aisp_initializeLanguage();

        // 初始化API管理器
        await aisp_initializeAPIManager();

        // 加载配置
        await aisp_loadConfiguration();

        // 初始化界面组件
        aisp_setupEventListeners();

        // 初始化聊天界面
        if (typeof ui_ChatInterface !== 'undefined') {
            aisp_chatInterface = new ui_ChatInterface('aisp-message-list');
        } else {
            console.warn('ChatInterface 组件未加载，使用简化版本');
            aisp_chatInterface = aisp_createSimpleChatInterface();
        }

        // 初始化回复建议组件
        await aisp_initializeReplySuggestions();

        // 初始化模板系统
        await aisp_initializeTemplateSystem();

        // 初始化Google Drive知识库
        await aisp_initializeKnowledgeBase();

        // 初始化性能优化系统
        await aisp_initializePerformanceSystem();

        // 加载当前页面信息
        await aisp_loadCurrentPageInfo();

        // 更新状态显示
        aisp_updateConnectionStatus();

        // 显示欢迎消息
        aisp_showWelcomeMessage();

        aisp_isInitialized = true;
        console.log('✅ AI Side Panel 侧边栏初始化完成');

    } catch (error) {
        console.error('❌ 侧边栏初始化失败:', error);
        const errorMessage = typeof lang_getErrorText === 'function'
            ? lang_getErrorText('initializationFailed')
            : '初始化失败，请刷新重试';
        aisp_showError(errorMessage + ': ' + error.message);
    }
}

/**
 * @function aisp_initializeLanguage - 初始化多语言支持
 * @description 加载语言资源并设置当前语言
 */
async function aisp_initializeLanguage() {
    try {
        // 检查language-manager是否可用
        if (typeof lang_initialize !== 'function') {
            console.warn('⚠️ Language Manager 未加载，跳过多语言初始化');
            return;
        }

        // 获取保存的语言设置
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        const savedLanguage = config.currentLanguage || 'zh_CN';

        // 初始化语言管理器
        await lang_initialize(savedLanguage);

        // 添加语言变更监听器
        lang_addChangeListener((newLang, oldLang) => {
            console.log(`🌐 侧边栏语言已切换: ${oldLang} -> ${newLang}`);
            // 更新连接状态显示（使用新语言）
            aisp_updateConnectionStatus();
        });

        console.log(`✅ 侧边栏多语言支持初始化完成，当前语言: ${savedLanguage}`);
    } catch (error) {
        console.error('❌ 侧边栏多语言初始化失败:', error);
    }
}

/**
 * @function aisp_initializeReplySuggestions - 初始化回复建议组件
 * @description 初始化智能回复建议系统
 */
async function aisp_initializeReplySuggestions() {
    try {
        // 检查回复建议组件是否可用
        if (typeof ReplySuggestions === 'undefined') {
            console.warn('⚠️ 回复建议组件未加载，跳过初始化');
            return;
        }

        // 查找回复建议容器
        const replySuggestionsContainer = document.getElementById('aisp-reply-suggestions');
        if (!replySuggestionsContainer) {
            console.warn('⚠️ 回复建议容器未找到，跳过初始化');
            return;
        }

        // 初始化回复建议组件
        aisp_replySuggestions = new ReplySuggestions(replySuggestionsContainer, {
            language: aisp_currentLanguage,
            showCopyButton: true,
            showTypeLabels: true,
            enableSelection: true,
            onReplySelect: (reply, index) => {
                console.log('选择了回复:', reply);
                // 可以在这里添加选择回复后的处理逻辑
            },
            onReplyCopy: (content) => {
                console.log('复制了回复:', content);
                aisp_addChatMessage('✅ 回复已复制到剪贴板', 'system');
            }
        });

        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                if (aisp_replySuggestions) {
                    aisp_replySuggestions.updateLanguage(newLang);
                }
            });
        }

        console.log('✅ 回复建议组件初始化完成');

    } catch (error) {
        console.error('❌ 回复建议组件初始化失败:', error);
    }
}

/**
 * @function aisp_initializeTemplateSystem - 初始化模板系统
 * @description 初始化快捷回复模板系统
 */
async function aisp_initializeTemplateSystem() {
    try {
        // 检查模板管理器是否可用
        if (typeof getTemplateManager === 'function') {
            aisp_templateManager = getTemplateManager();
            await aisp_templateManager.initialize();
        } else {
            console.warn('⚠️ 模板管理器未加载，跳过初始化');
            return;
        }

        // 检查模板弹窗组件是否可用
        if (typeof TemplatePopup === 'undefined') {
            console.warn('⚠️ 模板弹窗组件未加载，跳过初始化');
            return;
        }

        // 查找输入框
        const inputElement = document.getElementById('aisp-user-input');
        if (!inputElement) {
            console.warn('⚠️ 用户输入框未找到，跳过模板弹窗初始化');
            return;
        }

        // 初始化模板弹窗
        aisp_templatePopup = new TemplatePopup(inputElement, {
            language: aisp_currentLanguage,
            maxTemplates: 8,
            showPreview: true,
            enableSearch: true,
            enableKeyboard: true,
            autoShow: false, // 手动控制显示
            position: 'top',
            onTemplateSelect: (template) => {
                console.log('选择了模板:', template.title);
            },
            onTemplateInsert: (template) => {
                console.log('插入了模板:', template.title);
                aisp_addChatMessage(`📋 已插入模板: ${template.title}`, 'system');

                // 记录模板使用
                aisp_recordTemplateUsage(template);
            }
        });

        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                if (aisp_templatePopup) {
                    aisp_templatePopup.updateLanguage(newLang);
                }
            });
        }

        // 添加快捷键支持
        aisp_setupTemplateShortcuts();

        console.log('✅ 模板系统初始化完成');

    } catch (error) {
        console.error('❌ 模板系统初始化失败:', error);
    }
}

/**
 * @function aisp_setupTemplateShortcuts - 设置模板快捷键
 * @description 设置模板系统的快捷键
 */
function aisp_setupTemplateShortcuts() {
    const inputElement = document.getElementById('aisp-user-input');
    if (!inputElement) return;

    // 添加快捷键监听
    inputElement.addEventListener('keydown', (event) => {
        // Ctrl/Cmd + T 显示模板弹窗
        if ((event.ctrlKey || event.metaKey) && event.key === 't') {
            event.preventDefault();
            if (aisp_templatePopup) {
                aisp_templatePopup.show();
            }
        }

        // Ctrl/Cmd + Shift + T 智能预测模板
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
            event.preventDefault();
            aisp_showPredictedTemplates();
        }
    });
}

/**
 * @function aisp_showPredictedTemplates - 显示预测模板
 * @description 基于当前输入显示智能预测的模板
 */
async function aisp_showPredictedTemplates() {
    try {
        const inputElement = document.getElementById('aisp-user-input');
        if (!inputElement || !aisp_templateManager) return;

        const inputText = inputElement.value;
        if (!inputText.trim()) {
            // 如果没有输入，显示所有模板
            if (aisp_templatePopup) {
                aisp_templatePopup.show();
            }
            return;
        }

        // 获取页面上下文
        const pageContent = await aisp_getCurrentPageContent();
        const context = {
            pageTitle: pageContent.title,
            pageUrl: pageContent.url,
            inputText: inputText
        };

        // 智能预测模板
        const predictedTemplates = await aisp_templateManager.predictTemplates(inputText, context);

        if (predictedTemplates.length > 0) {
            console.log(`🔮 预测到 ${predictedTemplates.length} 个相关模板`);

            // 显示模板弹窗
            if (aisp_templatePopup) {
                aisp_templatePopup.show();
            }

            aisp_addChatMessage(`🔮 为您预测了 ${predictedTemplates.length} 个相关模板`, 'system');
        } else {
            aisp_addChatMessage('🔮 未找到相关模板建议', 'system');
        }

    } catch (error) {
        console.error('智能预测模板失败:', error);
        aisp_addChatMessage('❌ 模板预测失败', 'system');
    }
}

/**
 * @function aisp_recordTemplateUsage - 记录模板使用
 * @description 记录模板的使用情况，用于优化推荐
 * @param {Object} template - 使用的模板
 */
function aisp_recordTemplateUsage(template) {
    try {
        // 更新模板的使用时间
        if (aisp_templateManager) {
            aisp_templateManager.updateTemplate(template.id, {
                lastUsed: Date.now(),
                useCount: (template.useCount || 0) + 1
            });
        }

        console.log('📊 模板使用已记录:', template.title);

    } catch (error) {
        console.error('记录模板使用失败:', error);
    }
}

/**
 * @function aisp_initializeKnowledgeBase - 初始化Google Drive知识库
 * @description 初始化Google Drive知识库系统
 */
async function aisp_initializeKnowledgeBase() {
    try {
        // 检查Google Drive API是否可用
        if (typeof getGoogleDriveAPI === 'function') {
            aisp_googleDriveAPI = getGoogleDriveAPI();
            await aisp_googleDriveAPI.initialize();
        } else {
            console.warn('⚠️ Google Drive API未加载，跳过初始化');
            return;
        }

        // 检查知识库组件是否可用
        if (typeof KnowledgeBase === 'undefined') {
            console.warn('⚠️ 知识库组件未加载，跳过初始化');
            return;
        }

        // 查找知识库容器
        const knowledgeBaseContainer = document.getElementById('aisp-knowledge-base');
        if (!knowledgeBaseContainer) {
            console.warn('⚠️ 知识库容器未找到，跳过初始化');
            return;
        }

        // 初始化知识库组件
        aisp_knowledgeBase = new KnowledgeBase(knowledgeBaseContainer, {
            language: aisp_currentLanguage,
            showSyncButton: true,
            showAuthButton: true,
            showStorageInfo: true,
            autoSync: false, // 默认不自动同步
            syncInterval: 300000, // 5分钟
            onSyncComplete: (syncResult) => {
                console.log('知识库同步完成:', syncResult);
                aisp_addChatMessage(`☁️ 知识库同步完成: 模板${syncResult.templates.uploaded}个上传`, 'system');
            },
            onAuthComplete: () => {
                console.log('Google Drive认证完成');
                aisp_addChatMessage('☁️ Google Drive认证成功', 'system');
            },
            onError: (error) => {
                console.error('知识库错误:', error);
                aisp_addChatMessage(`❌ 知识库错误: ${error.message}`, 'system');
            }
        });

        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                if (aisp_knowledgeBase) {
                    aisp_knowledgeBase.updateLanguage(newLang);
                }
            });
        }

        console.log('✅ Google Drive知识库初始化完成');

    } catch (error) {
        console.error('❌ Google Drive知识库初始化失败:', error);
    }
}

/**
 * @function aisp_syncKnowledgeBase - 同步知识库
 * @description 手动触发知识库同步
 */
async function aisp_syncKnowledgeBase() {
    try {
        if (!aisp_knowledgeBase) {
            throw new Error('知识库组件未初始化');
        }

        const status = aisp_knowledgeBase.getStatus();
        if (!status.authStatus || !status.authStatus.isAuthenticated) {
            throw new Error('请先认证Google Drive');
        }

        aisp_addChatMessage('🔄 开始同步知识库...', 'system');

        // 触发同步
        await aisp_knowledgeBase.handleSync();

    } catch (error) {
        console.error('手动同步知识库失败:', error);
        aisp_addChatMessage(`❌ 同步失败: ${error.message}`, 'system');
    }
}

/**
 * @function aisp_authenticateGoogleDrive - 认证Google Drive
 * @description 手动触发Google Drive认证
 */
async function aisp_authenticateGoogleDrive() {
    try {
        if (!aisp_knowledgeBase) {
            throw new Error('知识库组件未初始化');
        }

        aisp_addChatMessage('🔐 开始Google Drive认证...', 'system');

        // 触发认证
        await aisp_knowledgeBase.handleAuth();

    } catch (error) {
        console.error('Google Drive认证失败:', error);
        aisp_addChatMessage(`❌ 认证失败: ${error.message}`, 'system');
    }
}

/**
 * @function aisp_getKnowledgeBaseStatus - 获取知识库状态
 * @description 获取Google Drive知识库状态信息
 * @returns {Object} 知识库状态
 */
function aisp_getKnowledgeBaseStatus() {
    if (!aisp_knowledgeBase) {
        return {
            available: false,
            message: '知识库组件未初始化'
        };
    }

    const status = aisp_knowledgeBase.getStatus();

    return {
        available: true,
        isInitialized: status.isInitialized,
        isAuthenticated: status.authStatus ? status.authStatus.isAuthenticated : false,
        isSyncing: status.isSyncing,
        lastSync: status.syncStatus ? status.syncStatus.lastSync : null,
        autoSyncEnabled: status.autoSyncEnabled
    };
}

/**
 * @function aisp_initializePerformanceSystem - 初始化性能优化系统
 * @description 初始化缓存管理器、性能优化器、测试框架和性能监控
 */
async function aisp_initializePerformanceSystem() {
    try {
        // 初始化缓存管理器
        if (typeof getCacheManager === 'function') {
            aisp_cacheManager = getCacheManager();
            await aisp_cacheManager.initialize();
            console.log('✅ 缓存管理器初始化完成');
        } else {
            console.warn('⚠️ 缓存管理器未加载，跳过初始化');
        }

        // 初始化性能优化器
        if (typeof getPerformanceOptimizer === 'function') {
            aisp_performanceOptimizer = getPerformanceOptimizer();
            await aisp_performanceOptimizer.initialize();
            console.log('✅ 性能优化器初始化完成');
        } else {
            console.warn('⚠️ 性能优化器未加载，跳过初始化');
        }

        // 初始化测试框架
        if (typeof getTestFramework === 'function') {
            aisp_testFramework = getTestFramework();
            await aisp_testFramework.initialize();
            console.log('✅ 测试框架初始化完成');
        } else {
            console.warn('⚠️ 测试框架未加载，跳过初始化');
        }

        // 初始化性能监控组件
        await aisp_initializePerformanceMonitor();

        // 优化现有API调用
        aisp_optimizeExistingApis();

        console.log('✅ 性能优化系统初始化完成');

    } catch (error) {
        console.error('❌ 性能优化系统初始化失败:', error);
    }
}

/**
 * @function aisp_initializePerformanceMonitor - 初始化性能监控组件
 * @description 初始化性能监控界面组件
 */
async function aisp_initializePerformanceMonitor() {
    try {
        // 检查性能监控组件是否可用
        if (typeof PerformanceMonitor === 'undefined') {
            console.warn('⚠️ 性能监控组件未加载，跳过初始化');
            return;
        }

        // 查找性能监控容器
        const performanceMonitorContainer = document.getElementById('aisp-performance-monitor');
        if (!performanceMonitorContainer) {
            console.warn('⚠️ 性能监控容器未找到，跳过初始化');
            return;
        }

        // 初始化性能监控组件
        aisp_performanceMonitor = new PerformanceMonitor(performanceMonitorContainer, {
            language: aisp_currentLanguage,
            updateInterval: 5000,      // 5秒更新间隔
            showDetailedMetrics: true,
            enableAutoTest: false,     // 默认不自动测试
            testInterval: 300000,      // 5分钟测试间隔
            onMetricsUpdate: (metrics) => {
                console.log('📊 性能指标已更新:', metrics);
            },
            onTestComplete: (testResults) => {
                console.log('🧪 测试完成:', testResults);
                aisp_addChatMessage(`🧪 测试完成: 总计${testResults.summary.functional.total + testResults.summary.compatibility.total + testResults.summary.performance.total + testResults.summary.userExperience.total}项`, 'system');
            },
            onError: (error) => {
                console.error('性能监控错误:', error);
                aisp_addChatMessage(`❌ 性能监控错误: ${error.message}`, 'system');
            }
        });

        // 添加语言变更监听器
        if (typeof lang_addChangeListener === 'function') {
            lang_addChangeListener((newLang, oldLang) => {
                if (aisp_performanceMonitor) {
                    aisp_performanceMonitor.updateLanguage(newLang);
                }
            });
        }

        console.log('✅ 性能监控组件初始化完成');

    } catch (error) {
        console.error('❌ 性能监控组件初始化失败:', error);
    }
}

/**
 * @function aisp_optimizeExistingApis - 优化现有API调用
 * @description 为现有的API调用添加性能优化
 */
function aisp_optimizeExistingApis() {
    try {
        if (!aisp_performanceOptimizer) return;

        // 优化Gemini API调用
        if (aisp_geminiApi && aisp_geminiApi.generateContent) {
            const originalGenerateContent = aisp_geminiApi.generateContent;
            aisp_geminiApi.generateContent = aisp_performanceOptimizer.optimizeApiCall(
                originalGenerateContent.bind(aisp_geminiApi),
                {
                    cacheType: 'api_responses',
                    cacheTTL: 300000, // 5分钟缓存
                    enableDeduplication: true,
                    enableRetry: true
                }
            );
        }

        // 优化Google Drive API调用
        if (aisp_googleDriveAPI && aisp_googleDriveAPI.listFiles) {
            const originalListFiles = aisp_googleDriveAPI.listFiles;
            aisp_googleDriveAPI.listFiles = aisp_performanceOptimizer.optimizeApiCall(
                originalListFiles.bind(aisp_googleDriveAPI),
                {
                    cacheType: 'file_lists',
                    cacheTTL: 180000, // 3分钟缓存
                    enableDeduplication: true
                }
            );
        }

        // 优化模板管理器调用
        if (aisp_templateManager && aisp_templateManager.getTemplates) {
            const originalGetTemplates = aisp_templateManager.getTemplates;
            aisp_templateManager.getTemplates = aisp_performanceOptimizer.optimizeApiCall(
                originalGetTemplates.bind(aisp_templateManager),
                {
                    cacheType: 'templates',
                    cacheTTL: 600000, // 10分钟缓存
                    enableDeduplication: true
                }
            );
        }

        console.log('✅ 现有API调用已优化');

    } catch (error) {
        console.error('优化现有API调用失败:', error);
    }
}

/**
 * @function aisp_runPerformanceTests - 运行性能测试
 * @description 手动触发性能测试
 */
async function aisp_runPerformanceTests() {
    try {
        if (!aisp_testFramework) {
            throw new Error('测试框架未初始化');
        }

        aisp_addChatMessage('🧪 开始运行性能测试...', 'system');

        const testResults = await aisp_testFramework.runAllTests();

        // 生成测试报告
        const report = aisp_testFramework.generateTestReport(testResults);
        console.log('📋 测试报告:\n', report);

        // 显示测试结果摘要
        const summary = testResults.summary;
        const totalTests = Object.values(summary).reduce((sum, s) => sum + s.total, 0);
        const totalPassed = Object.values(summary).reduce((sum, s) => sum + s.passed, 0);
        const totalFailed = Object.values(summary).reduce((sum, s) => sum + s.failed, 0);

        aisp_addChatMessage(`🧪 测试完成: ${totalPassed}通过/${totalTests}总计 (${totalFailed}失败)`, 'system');

        return testResults;

    } catch (error) {
        console.error('运行性能测试失败:', error);
        aisp_addChatMessage(`❌ 测试失败: ${error.message}`, 'system');
        throw error;
    }
}

/**
 * @function aisp_getPerformanceMetrics - 获取性能指标
 * @description 获取当前的性能指标
 * @returns {Object} 性能指标
 */
function aisp_getPerformanceMetrics() {
    if (!aisp_performanceOptimizer) {
        return {
            available: false,
            message: '性能优化器未初始化'
        };
    }

    const metrics = aisp_performanceOptimizer.getMetrics();

    return {
        available: true,
        metrics: metrics,
        cache: aisp_cacheManager ? aisp_cacheManager.getStats() : null
    };
}

/**
 * @function aisp_clearCache - 清空缓存
 * @description 清空所有缓存数据
 */
function aisp_clearCache() {
    try {
        if (aisp_cacheManager) {
            aisp_cacheManager.clearAll();
            aisp_addChatMessage('🧹 缓存已清空', 'system');
        } else {
            aisp_addChatMessage('⚠️ 缓存管理器不可用', 'system');
        }
    } catch (error) {
        console.error('清空缓存失败:', error);
        aisp_addChatMessage(`❌ 清空缓存失败: ${error.message}`, 'system');
    }
}

/**
 * @function aisp_initializeAPIManager - 初始化API管理器
 * @description 初始化配置管理器和API管理器
 */
async function aisp_initializeAPIManager() {
    try {
        // 初始化配置管理器
        if (typeof getConfigManager === 'function') {
            aisp_configManager = getConfigManager();
            await aisp_configManager.initialize();
        }

        // 初始化API管理器
        if (typeof getAPIManager === 'function') {
            aisp_apiManager = getAPIManager();
            await aisp_apiManager.initialize();

            if (aisp_apiManager.isReady()) {
                console.log('✅ API管理器初始化成功');
            } else {
                console.warn('⚠️ API管理器未就绪，请检查API密钥配置');
            }
        } else {
            console.warn('⚠️ API管理器未加载，某些功能可能不可用');
        }

    } catch (error) {
        console.error('❌ API管理器初始化失败:', error);
        throw error;
    }
}

/**
 * @function aisp_loadConfiguration - 加载配置
 * @description 从存储中加载用户配置和设置
 */
async function aisp_loadConfiguration() {
    try {
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        
        // 设置语言
        aisp_currentLanguage = config.currentLanguage || 'zh_CN';
        const languageSelect = document.getElementById('aisp-language-select');
        if (languageSelect) {
            languageSelect.value = aisp_currentLanguage;
        }
        
        // 应用语言设置
        await lang_setCurrentLanguage(aisp_currentLanguage);
        
        console.log('配置加载完成:', config);
    } catch (error) {
        console.error('加载配置失败:', error);
    }
}

/**
 * @function aisp_setupEventListeners - 设置事件监听器
 * @description 为界面元素添加事件监听器
 */
function aisp_setupEventListeners() {
    // 语言切换
    const languageSelect = document.getElementById('aisp-language-select');
    if (languageSelect) {
        languageSelect.addEventListener('change', aisp_handleLanguageChange);
    }
    
    // 设置按钮
    const settingsBtn = document.getElementById('aisp-settings-btn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', aisp_openSettings);
    }
    
    // 刷新按钮
    const refreshBtn = document.getElementById('aisp-refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', aisp_refreshAnalysis);
    }
    
    // 分析当前页面按钮
    const analyzeBtn = document.getElementById('aisp-analyze-current-page');
    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', aisp_analyzeCurrentPage);
    }
    
    // 发送按钮
    const sendBtn = document.getElementById('aisp-send-btn');
    if (sendBtn) {
        sendBtn.addEventListener('click', aisp_handleSendMessage);
    }
    
    // 清空按钮
    const clearBtn = document.getElementById('aisp-clear-btn');
    if (clearBtn) {
        clearBtn.addEventListener('click', aisp_clearInput);
    }
    
    // 输入框回车事件
    const userInput = document.getElementById('aisp-user-input');
    if (userInput) {
        userInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                aisp_handleSendMessage();
            }
        });
    }
    
    // 模板管理按钮
    const templateBtn = document.getElementById('aisp-template-manager-btn');
    if (templateBtn) {
        templateBtn.addEventListener('click', aisp_openTemplateManager);
    }
    
    // 知识库按钮
    const knowledgeBtn = document.getElementById('aisp-knowledge-base-btn');
    if (knowledgeBtn) {
        knowledgeBtn.addEventListener('click', aisp_openKnowledgeBase);
    }
    
    // 模态框关闭
    const modalClose = document.getElementById('aisp-modal-close');
    const modalOverlay = document.getElementById('aisp-modal-overlay');
    const modalCancel = document.getElementById('aisp-modal-cancel');
    
    if (modalClose) modalClose.addEventListener('click', aisp_closeModal);
    if (modalCancel) modalCancel.addEventListener('click', aisp_closeModal);
    if (modalOverlay) {
        modalOverlay.addEventListener('click', (event) => {
            if (event.target === modalOverlay) {
                aisp_closeModal();
            }
        });
    }
}
// #endregion

// #region 页面信息处理
/**
 * @function aisp_loadCurrentPageInfo - 加载当前页面信息
 * @description 获取并显示当前标签页的信息
 */
async function aisp_loadCurrentPageInfo() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab) {
            const pageTitle = document.getElementById('aisp-page-title');
            if (pageTitle) {
                pageTitle.textContent = tab.title || '未知页面';
            }
            
            // 存储当前页面数据
            aisp_currentPageData = {
                url: tab.url,
                title: tab.title,
                tabId: tab.id
            };
        }
    } catch (error) {
        console.error('加载页面信息失败:', error);
    }
}

/**
 * @function aisp_updateConnectionStatus - 更新连接状态
 * @description 更新底部状态栏的连接状态显示
 */
function aisp_updateConnectionStatus() {
    const statusElement = document.getElementById('aisp-connection-status');
    const statusDot = statusElement?.querySelector('.aisp-status-dot');

    if (statusElement && statusDot) {
        try {
            // 检查硬编码的API密钥状态
            let isConnected = false;
            let statusText = '未连接';

            if (typeof getApiKeyStatus === 'function') {
                const keyStatus = getApiKeyStatus();
                isConnected = keyStatus.gemini.available;
                statusText = isConnected ? '已连接' : '需要配置API密钥';

                // 如果API管理器也已就绪，显示更详细的状态
                if (aisp_apiManager && aisp_apiManager.isReady()) {
                    statusText = '已连接 (Gemini 2.0)';
                }
            }

            statusDot.className = `aisp-status-dot ${isConnected ? 'aisp-status-connected' : 'aisp-status-disconnected'}`;

            // 更新状态文本
            const statusTextElement = statusElement.querySelector('.aisp-status-text');
            if (statusTextElement) {
                statusTextElement.textContent = statusText;
            } else {
                // 如果没有专门的文本元素，更新最后一个文本节点
                const textNode = Array.from(statusElement.childNodes).find(node => node.nodeType === Node.TEXT_NODE);
                if (textNode) {
                    textNode.textContent = statusText;
                }
            }

        } catch (error) {
            console.error('更新连接状态失败:', error);
            statusDot.className = 'aisp-status-dot aisp-status-disconnected';
        }
    }
}
// #endregion

// #region 事件处理函数
/**
 * @function aisp_handleLanguageChange - 处理语言切换
 * @param {Event} event - 事件对象
 */
async function aisp_handleLanguageChange(event) {
    try {
        const newLanguage = event.target.value;
        aisp_currentLanguage = newLanguage;
        
        // 保存语言设置
        const result = await chrome.storage.local.get('aisp_config');
        const config = result.aisp_config || {};
        config.currentLanguage = newLanguage;
        await chrome.storage.local.set({ 'aisp_config': config });
        
        // 应用语言设置
        await lang_setCurrentLanguage(newLanguage);
        
        // 更新界面文本
        aisp_updateUILanguage();
        
        console.log('语言已切换到:', newLanguage);
    } catch (error) {
        console.error('切换语言失败:', error);
        aisp_showError('语言切换失败');
    }
}

/**
 * @function aisp_openSettings - 打开设置页面
 */
function aisp_openSettings() {
    chrome.tabs.create({
        url: chrome.runtime.getURL('src/settings/settings.html')
    });
}

/**
 * @function aisp_refreshAnalysis - 刷新分析
 */
async function aisp_refreshAnalysis() {
    if (aisp_isAnalyzing) return;
    
    await aisp_analyzeCurrentPage();
}

/**
 * @function aisp_analyzeCurrentPage - 分析当前页面
 * @description 触发对当前页面的AI分析
 */
async function aisp_analyzeCurrentPage() {
    if (aisp_isAnalyzing) return;

    try {
        aisp_isAnalyzing = true;
        aisp_showLoading(true);

        // 隐藏之前的分析结果
        const resultContainer = document.getElementById('aisp-analysis-result');
        if (resultContainer) {
            resultContainer.style.display = 'none';
        }

        // 检查API管理器是否就绪
        if (!aisp_apiManager || !aisp_apiManager.isReady()) {
            throw new Error('AI服务未就绪，请检查API密钥配置');
        }

        // 获取当前页面内容
        const pageContent = await aisp_getCurrentPageContent();

        // 检查缓存
        const cacheKey = aisp_generateCacheKey(pageContent);
        if (aisp_analysisCache.has(cacheKey)) {
            const cachedResult = aisp_analysisCache.get(cacheKey);
            aisp_displayAnalysisResult(cachedResult);
            aisp_addChatMessage('📄 已显示缓存的分析结果', 'system');
            return;
        }

        // 使用API管理器分析内容
        aisp_addChatMessage('🔍 正在分析当前页面...', 'system');

        const analysisResult = await aisp_apiManager.analyzeContent({
            text: pageContent.text,
            url: pageContent.url,
            title: pageContent.title,
            structuredContent: pageContent.structuredContent,
            metadata: pageContent.metadata
        });

        // 缓存结果
        aisp_analysisCache.set(cacheKey, analysisResult);

        // 显示分析结果
        aisp_displayAnalysisResult(analysisResult);

        // 添加成功消息
        aisp_addChatMessage('✅ 页面分析完成', 'system');

    } catch (error) {
        console.error('分析页面失败:', error);
        const errorMessage = error.userMessage || error.message || '分析失败';
        aisp_showError('页面分析失败: ' + errorMessage);
    } finally {
        aisp_isAnalyzing = false;
        aisp_showLoading(false);
    }
}

/**
 * @function aisp_handleSendMessage - 处理发送消息
 */
async function aisp_handleSendMessage() {
    const userInput = document.getElementById('aisp-user-input');
    if (!userInput) return;

    const message = userInput.value.trim();
    if (!message) return;

    try {
        // 清空输入框
        userInput.value = '';

        // 显示加载状态
        aisp_showLoading(true);

        // 使用新的用户输入处理函数
        await aisp_handleUserInput(message);

    } catch (error) {
        console.error('发送消息失败:', error);
        aisp_showError('消息发送失败: ' + error.message);
    } finally {
        aisp_showLoading(false);
    }
}

/**
 * @function aisp_clearInput - 清空输入框
 */
function aisp_clearInput() {
    const userInput = document.getElementById('aisp-user-input');
    if (userInput) {
        userInput.value = '';
        userInput.focus();
    }
}

/**
 * @function aisp_openTemplateManager - 打开模板管理器
 */
function aisp_openTemplateManager() {
    aisp_showModal('模板管理', '模板管理功能正在开发中...', [
        { text: '关闭', action: aisp_closeModal }
    ]);
}

/**
 * @function aisp_openKnowledgeBase - 打开知识库
 */
function aisp_openKnowledgeBase() {
    aisp_showModal('知识库', '知识库功能正在开发中...', [
        { text: '关闭', action: aisp_closeModal }
    ]);
}
// #endregion

// #region UI辅助函数
/**
 * @function aisp_showLoading - 显示/隐藏加载状态
 * @param {boolean} show - 是否显示加载状态
 */
function aisp_showLoading(show) {
    const loadingElement = document.getElementById('aisp-loading');
    if (loadingElement) {
        loadingElement.style.display = show ? 'flex' : 'none';
    }
}

/**
 * @function aisp_displayAnalysisResult - 显示分析结果
 * @param {Object} result - 分析结果数据
 */
function aisp_displayAnalysisResult(result) {
    const resultContainer = document.getElementById('aisp-analysis-result');
    if (!resultContainer) return;
    
    // 更新总结内容
    const summaryContent = document.getElementById('aisp-summary-content');
    if (summaryContent) {
        summaryContent.textContent = result.summary || '暂无总结内容';
    }
    
    // 更新关键要点
    const keypointsContent = document.getElementById('aisp-keypoints-content');
    if (keypointsContent) {
        if (result.keyPoints && result.keyPoints.length > 0) {
            keypointsContent.innerHTML = result.keyPoints.map(point => 
                `<div class="aisp-keypoint">• ${point}</div>`
            ).join('');
        } else {
            keypointsContent.textContent = '暂无关键要点';
        }
    }
    
    // 显示结果容器
    resultContainer.style.display = 'block';
}

/**
 * @function aisp_showError - 显示错误消息
 * @param {string} message - 错误消息
 */
function aisp_showError(message) {
    if (aisp_chatInterface) {
        aisp_chatInterface.addMessage(`❌ ${message}`, 'system');
    }
}

/**
 * @function aisp_showModal - 显示模态框
 * @param {string} title - 标题
 * @param {string} content - 内容
 * @param {Array} actions - 操作按钮数组
 */
function aisp_showModal(title, content, actions = []) {
    const modalOverlay = document.getElementById('aisp-modal-overlay');
    const modalTitle = document.getElementById('aisp-modal-title');
    const modalContent = document.getElementById('aisp-modal-content');
    
    if (modalOverlay && modalTitle && modalContent) {
        modalTitle.textContent = title;
        modalContent.innerHTML = content;
        modalOverlay.style.display = 'flex';
    }
}

/**
 * @function aisp_closeModal - 关闭模态框
 */
function aisp_closeModal() {
    const modalOverlay = document.getElementById('aisp-modal-overlay');
    if (modalOverlay) {
        modalOverlay.style.display = 'none';
    }
}

/**
 * @function aisp_updateUILanguage - 更新界面语言
 * @description 根据当前语言设置更新界面文本
 */
function aisp_updateUILanguage() {
    // 这里将在多语言模块完成后实现
    console.log('更新界面语言到:', aisp_currentLanguage);
}
// #endregion

// #region 脚本启动
// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', aisp_initializeSidePanel);
} else {
    aisp_initializeSidePanel();
}

console.log('AI Side Panel Sidepanel Script 已加载');
// #endregion

// #region 新增辅助函数

/**
 * @function aisp_getCurrentPageContent - 获取当前页面内容
 * @description 从content script获取当前页面的内容数据
 * @returns {Promise<Object>} 页面内容数据
 */
async function aisp_getCurrentPageContent() {
    try {
        // 获取当前活动标签页
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        if (!tab) {
            throw new Error('无法获取当前标签页');
        }

        // 向content script请求页面内容
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'get_page_content'
        });

        if (response && response.success) {
            return response.data;
        } else {
            throw new Error(response?.error || '无法获取页面内容');
        }

    } catch (error) {
        console.error('获取页面内容失败:', error);

        // 返回基础页面信息作为后备
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        return {
            text: '无法获取页面内容，请刷新页面后重试',
            url: tab?.url || '',
            title: tab?.title || '未知页面',
            structuredContent: {},
            metadata: {}
        };
    }
}

/**
 * @function aisp_generateCacheKey - 生成缓存键
 * @description 基于页面内容生成缓存键
 * @param {Object} pageContent - 页面内容
 * @returns {string} 缓存键
 */
function aisp_generateCacheKey(pageContent) {
    const content = pageContent.text + pageContent.url + pageContent.title;
    return btoa(content).substring(0, 32); // 简单的base64编码作为缓存键
}

/**
 * @function aisp_addChatMessage - 添加聊天消息
 * @description 向聊天界面添加消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 ('user', 'ai', 'system')
 */
function aisp_addChatMessage(message, type = 'system') {
    if (aisp_chatInterface && typeof aisp_chatInterface.addMessage === 'function') {
        aisp_chatInterface.addMessage(message, type);
    } else {
        // 简化版本的消息显示
        const messageList = document.getElementById('aisp-message-list');
        if (messageList) {
            const messageElement = document.createElement('div');
            messageElement.className = `aisp-message aisp-message-${type}`;
            messageElement.innerHTML = `
                <div class="aisp-message-content">${message}</div>
                <div class="aisp-message-time">${new Date().toLocaleTimeString()}</div>
            `;
            messageList.appendChild(messageElement);
            messageList.scrollTop = messageList.scrollHeight;
        }
    }

    // 保存到消息历史
    aisp_messageHistory.push({
        message: message,
        type: type,
        timestamp: Date.now()
    });
}

/**
 * @function aisp_createSimpleChatInterface - 创建简化的聊天界面
 * @description 当ChatInterface组件不可用时的后备方案
 * @returns {Object} 简化的聊天界面对象
 */
function aisp_createSimpleChatInterface() {
    return {
        addMessage: (message, type) => {
            aisp_addChatMessage(message, type);
        },
        clear: () => {
            const messageList = document.getElementById('aisp-message-list');
            if (messageList) {
                messageList.innerHTML = '';
            }
            aisp_messageHistory = [];
        }
    };
}

/**
 * @function aisp_showWelcomeMessage - 显示欢迎消息
 * @description 在侧边栏初始化时显示欢迎消息
 */
function aisp_showWelcomeMessage() {
    const keyStatus = getApiKeyStatus ? getApiKeyStatus() : null;

    if (keyStatus && keyStatus.gemini.available) {
        const welcomeMsg = typeof lang_getSidePanelText === 'function'
            ? '🎉 ' + lang_getSidePanelText('welcome')
            : '🎉 欢迎使用 AI Side Panel！';
        aisp_addChatMessage(welcomeMsg, 'system');

        const readyMsg = '✅ Gemini 2.0 Flash 已就绪，您可以开始分析页面内容了。';
        aisp_addChatMessage(readyMsg, 'system');
    } else {
        aisp_addChatMessage('⚠️ 请配置 API 密钥以启用 AI 功能', 'system');
        aisp_addChatMessage('📖 查看 API_KEY_SETUP.md 了解配置方法', 'system');
    }
}

/**
 * @function aisp_handleUserInput - 处理用户输入
 * @description 处理用户在输入框中的消息
 * @param {string} userInput - 用户输入的内容
 */
async function aisp_handleUserInput(userInput) {
    if (!userInput.trim()) return;

    try {
        // 添加用户消息
        aisp_addChatMessage(userInput, 'user');

        // 检查API管理器
        if (!aisp_apiManager || !aisp_apiManager.isReady()) {
            aisp_addChatMessage('❌ AI服务未就绪，请检查API密钥配置', 'system');
            return;
        }

        // 显示正在处理的消息
        aisp_addChatMessage('🤔 正在思考...', 'system');

        // 获取当前页面内容作为上下文
        const pageContent = await aisp_getCurrentPageContent();
        const context = {
            pageTitle: pageContent.title,
            pageUrl: pageContent.url,
            pageContent: pageContent.text.substring(0, 500),
            previousMessages: aisp_messageHistory.slice(-3).map(msg => msg.message)
        };

        // 并行处理：生成AI回复和回复建议
        const [aiReply, replySuggestions] = await Promise.allSettled([
            // 生成AI回复
            aisp_apiManager.generateReplies(`当前页面：${context.pageTitle}\n内容摘要：${context.pageContent}...\n\n用户问题：${userInput}`, {
                type: 'helpful',
                tone: 'friendly'
            }),
            // 生成回复建议
            aisp_generateReplySuggestions(userInput, context)
        ]);

        // 处理AI回复
        if (aiReply.status === 'fulfilled' && aiReply.value && aiReply.value.length > 0) {
            aisp_addChatMessage(aiReply.value[0], 'ai');
        } else {
            aisp_addChatMessage('抱歉，我暂时无法回答这个问题。', 'ai');
        }

        // 处理回复建议（如果失败也不影响主要功能）
        if (replySuggestions.status === 'rejected') {
            console.warn('回复建议生成失败:', replySuggestions.reason);
        }

    } catch (error) {
        console.error('处理用户输入失败:', error);
        const errorMessage = error.userMessage || error.message || '处理失败';
        aisp_addChatMessage('❌ ' + errorMessage, 'system');
    }
}

/**
 * @function aisp_generateReplySuggestions - 生成回复建议
 * @description 为用户输入生成智能回复建议
 * @param {string} userInput - 用户输入
 * @param {Object} context - 上下文信息
 */
async function aisp_generateReplySuggestions(userInput, context) {
    try {
        if (!aisp_replySuggestions) {
            console.warn('回复建议组件未初始化');
            return;
        }

        // 生成回复建议
        const replies = await aisp_replySuggestions.generateReplies(userInput, context, {
            language: aisp_currentLanguage,
            maxLength: 200,
            includeEmoji: true
        });

        if (replies && replies.length > 0) {
            aisp_addChatMessage(`💬 已为您生成 ${replies.length} 个回复建议`, 'system');
        }

        return replies;

    } catch (error) {
        console.error('生成回复建议失败:', error);
        throw error;
    }
}

// #endregion
