/**
 * @file AI Side Panel 语言管理器
 * @description 处理多语言支持，包括语言切换、文本获取、资源加载等功能
 */

// #region 全局变量
let lang_currentLanguage = 'zh_CN';
let lang_messages = {};
let lang_isLoaded = false;
let lang_loadingPromise = null;
let lang_changeListeners = [];
let lang_fallbackMessages = {};

// 支持的语言列表
const SUPPORTED_LANGUAGES = {
    'zh_CN': '中文',
    'en_US': 'English',
    'ja_JP': '日本語',
    'ko_KR': '한국어'
};

// 语言代码映射（支持更多格式）
const LANGUAGE_CODE_MAPPING = {
    'zh': 'zh_CN',
    'zh-cn': 'zh_CN',
    'zh-hans': 'zh_CN',
    'en': 'en_US',
    'en-us': 'en_US',
    'ja': 'ja_JP',
    'jp': 'ja_JP',
    'ko': 'ko_KR',
    'kr': 'ko_KR'
};

// 语言方向配置
const LANGUAGE_DIRECTIONS = {
    'zh_CN': 'ltr',
    'en_US': 'ltr',
    'ja_JP': 'ltr',
    'ko_KR': 'ltr'
};
// #endregion

// #region 语言资源加载
/**
 * @function lang_loadResources - 加载语言资源
 * @description 从JSON文件加载指定语言的资源
 * @param {string} language - 语言代码
 * @returns {Promise<Object>} 语言资源对象
 */
async function lang_loadResources(language) {
    try {
        // 标准化语言代码
        const normalizedLang = lang_normalizeLanguageCode(language);

        const response = await fetch(chrome.runtime.getURL(`i18n/${normalizedLang}/messages.json`));
        if (!response.ok) {
            throw new Error(`Failed to load language resources for ${normalizedLang} (HTTP ${response.status})`);
        }

        const messages = await response.json();

        // 验证消息格式
        if (!lang_validateMessages(messages)) {
            throw new Error(`Invalid message format for ${normalizedLang}`);
        }

        console.log(`✅ 语言资源加载成功: ${normalizedLang}`);
        return messages;
    } catch (error) {
        console.error(`❌ 加载语言资源失败: ${language}`, error);

        // 如果加载失败，尝试加载默认语言（中文）
        if (language !== 'zh_CN') {
            console.log('🔄 尝试加载默认语言资源...');
            return await lang_loadResources('zh_CN');
        }

        // 如果连默认语言都加载失败，返回后备消息
        console.warn('⚠️ 使用内置后备消息');
        return lang_getFallbackMessages();
    }
}

/**
 * @function lang_normalizeLanguageCode - 标准化语言代码
 * @description 将各种格式的语言代码转换为标准格式
 * @param {string} language - 原始语言代码
 * @returns {string} 标准化的语言代码
 */
function lang_normalizeLanguageCode(language) {
    if (!language) return 'zh_CN';

    const lowerLang = language.toLowerCase();

    // 直接匹配
    if (SUPPORTED_LANGUAGES[language]) {
        return language;
    }

    // 映射匹配
    if (LANGUAGE_CODE_MAPPING[lowerLang]) {
        return LANGUAGE_CODE_MAPPING[lowerLang];
    }

    // 前缀匹配
    const langPrefix = lowerLang.split('-')[0];
    const matchedLang = Object.keys(SUPPORTED_LANGUAGES).find(lang =>
        lang.toLowerCase().startsWith(langPrefix)
    );

    return matchedLang || 'zh_CN';
}

/**
 * @function lang_validateMessages - 验证消息格式
 * @description 验证加载的消息是否符合预期格式
 * @param {Object} messages - 消息对象
 * @returns {boolean} 是否有效
 */
function lang_validateMessages(messages) {
    if (!messages || typeof messages !== 'object') {
        return false;
    }

    // 检查必需的顶级键
    const requiredKeys = ['common', 'sidepanel', 'errors'];
    for (const key of requiredKeys) {
        if (!messages[key] || typeof messages[key] !== 'object') {
            console.warn(`缺少必需的消息键: ${key}`);
            return false;
        }
    }

    return true;
}

/**
 * @function lang_getFallbackMessages - 获取后备消息
 * @description 当所有语言资源都加载失败时使用的后备消息
 * @returns {Object} 后备消息对象
 */
function lang_getFallbackMessages() {
    return {
        common: {
            ok: { message: 'OK' },
            cancel: { message: 'Cancel' },
            close: { message: 'Close' },
            save: { message: 'Save' },
            send: { message: 'Send' },
            loading: { message: 'Loading...' },
            error: { message: 'Error' }
        },
        sidepanel: {
            title: { message: 'AI Side Panel' },
            welcome: { message: 'Welcome' },
            analyzing: { message: 'Analyzing...' }
        },
        errors: {
            initializationFailed: { message: 'Initialization failed' },
            analysisFailed: { message: 'Analysis failed' }
        }
    };
}

/**
 * @function lang_initializeLanguage - 初始化语言系统
 * @description 初始化语言管理器，加载默认语言资源
 * @param {string} defaultLanguage - 默认语言代码
 * @returns {Promise<void>}
 */
async function lang_initializeLanguage(defaultLanguage = 'zh_CN') {
    try {
        lang_currentLanguage = defaultLanguage;
        lang_messages = await lang_loadResources(defaultLanguage);
        lang_isLoaded = true;
        
        console.log('语言系统初始化完成:', defaultLanguage);
    } catch (error) {
        console.error('语言系统初始化失败:', error);
        lang_isLoaded = false;
    }
}

/**
 * @function lang_setCurrentLanguage - 设置当前语言
 * @description 切换到指定语言并加载相应资源
 * @param {string} language - 语言代码
 * @param {boolean} updateDOM - 是否自动更新DOM
 * @returns {Promise<boolean>} 是否切换成功
 */
async function lang_setCurrentLanguage(language, updateDOM = true) {
    const normalizedLang = lang_normalizeLanguageCode(language);

    if (!SUPPORTED_LANGUAGES[normalizedLang]) {
        console.warn(`不支持的语言: ${language} (标准化为: ${normalizedLang})`);
        return false;
    }

    if (normalizedLang === lang_currentLanguage && lang_isLoaded) {
        return true;
    }

    try {
        // 防止重复加载
        if (lang_loadingPromise) {
            await lang_loadingPromise;
        }

        lang_loadingPromise = lang_loadResources(normalizedLang);
        const messages = await lang_loadingPromise;

        const previousLanguage = lang_currentLanguage;
        lang_currentLanguage = normalizedLang;
        lang_messages = messages;
        lang_isLoaded = true;
        lang_loadingPromise = null;

        // 更新页面语言属性
        if (updateDOM && typeof document !== 'undefined') {
            document.documentElement.lang = normalizedLang;
            document.documentElement.dir = LANGUAGE_DIRECTIONS[normalizedLang] || 'ltr';

            // 自动更新页面文本
            lang_updatePageLanguage();
        }

        // 触发语言变更事件
        lang_notifyLanguageChange(normalizedLang, previousLanguage);

        console.log(`✅ 语言已切换到: ${normalizedLang}`);
        return true;
    } catch (error) {
        console.error(`❌ 语言切换失败: ${language}`, error);
        lang_loadingPromise = null;
        return false;
    }
}

/**
 * @function lang_addChangeListener - 添加语言变更监听器
 * @description 添加语言变更时的回调函数
 * @param {Function} listener - 监听器函数 (newLang, oldLang) => void
 */
function lang_addChangeListener(listener) {
    if (typeof listener === 'function') {
        lang_changeListeners.push(listener);
    }
}

/**
 * @function lang_removeChangeListener - 移除语言变更监听器
 * @description 移除指定的语言变更监听器
 * @param {Function} listener - 要移除的监听器函数
 */
function lang_removeChangeListener(listener) {
    const index = lang_changeListeners.indexOf(listener);
    if (index > -1) {
        lang_changeListeners.splice(index, 1);
    }
}

/**
 * @function lang_notifyLanguageChange - 通知语言变更
 * @description 通知所有监听器语言已变更
 * @param {string} newLanguage - 新语言
 * @param {string} oldLanguage - 旧语言
 */
function lang_notifyLanguageChange(newLanguage, oldLanguage) {
    lang_changeListeners.forEach(listener => {
        try {
            listener(newLanguage, oldLanguage);
        } catch (error) {
            console.error('语言变更监听器执行失败:', error);
        }
    });
}
// #endregion

// #region 文本获取函数
/**
 * @function lang_getText - 获取本地化文本
 * @description 根据键路径获取当前语言的文本
 * @param {string} keyPath - 键路径，支持点号分隔的嵌套路径
 * @param {Object} params - 参数对象，用于文本插值
 * @returns {string} 本地化文本
 */
function lang_getText(keyPath, params = {}) {
    if (!lang_isLoaded || !lang_messages) {
        console.warn('语言资源未加载，返回键路径:', keyPath);
        return keyPath;
    }
    
    // 解析嵌套键路径
    const keys = keyPath.split('.');
    let current = lang_messages;
    
    for (const key of keys) {
        if (current && typeof current === 'object' && current[key]) {
            current = current[key];
        } else {
            console.warn(`未找到文本键: ${keyPath}`);
            return keyPath;
        }
    }
    
    // 获取最终的消息对象
    let message = '';
    if (typeof current === 'object' && current.message) {
        message = current.message;
    } else if (typeof current === 'string') {
        message = current;
    } else {
        console.warn(`无效的文本格式: ${keyPath}`, current);
        return keyPath;
    }
    
    // 处理参数插值
    if (Object.keys(params).length > 0) {
        message = lang_interpolateText(message, params);
    }
    
    return message;
}

/**
 * @function lang_interpolateText - 文本插值
 * @description 将参数插入到文本中的占位符
 * @param {string} text - 包含占位符的文本
 * @param {Object} params - 参数对象
 * @returns {string} 插值后的文本
 */
function lang_interpolateText(text, params) {
    let result = text;
    
    Object.entries(params).forEach(([key, value]) => {
        const placeholder = `{${key}}`;
        result = result.replace(new RegExp(placeholder, 'g'), value);
    });
    
    return result;
}

/**
 * @function lang_getCommonText - 获取通用文本
 * @description 获取common命名空间下的文本
 * @param {string} key - 文本键
 * @param {Object} params - 参数对象
 * @returns {string} 本地化文本
 */
function lang_getCommonText(key, params = {}) {
    return lang_getText(`common.${key}`, params);
}

/**
 * @function lang_getErrorText - 获取错误文本
 * @description 获取errors命名空间下的文本
 * @param {string} key - 错误键
 * @param {Object} params - 参数对象
 * @returns {string} 错误文本
 */
function lang_getErrorText(key, params = {}) {
    return lang_getText(`errors.${key}`, params);
}

/**
 * @function lang_getSuccessText - 获取成功文本
 * @description 获取success命名空间下的文本
 * @param {string} key - 成功键
 * @param {Object} params - 参数对象
 * @returns {string} 成功文本
 */
function lang_getSuccessText(key, params = {}) {
    return lang_getText(`success.${key}`, params);
}

/**
 * @function lang_getSidePanelText - 获取侧边栏文本
 * @description 获取sidepanel命名空间下的文本
 * @param {string} key - 文本键
 * @param {Object} params - 参数对象
 * @returns {string} 侧边栏文本
 */
function lang_getSidePanelText(key, params = {}) {
    return lang_getText(`sidepanel.${key}`, params);
}

/**
 * @function lang_getAnalysisText - 获取分析文本
 * @description 获取analysis命名空间下的文本
 * @param {string} key - 文本键
 * @param {Object} params - 参数对象
 * @returns {string} 分析文本
 */
function lang_getAnalysisText(key, params = {}) {
    return lang_getText(`analysis.${key}`, params);
}

/**
 * @function lang_getTemplateText - 获取模板文本
 * @description 获取templates命名空间下的文本
 * @param {string} key - 文本键
 * @param {Object} params - 参数对象
 * @returns {string} 模板文本
 */
function lang_getTemplateText(key, params = {}) {
    return lang_getText(`templates.${key}`, params);
}

/**
 * @function lang_getPopupText - 获取弹窗文本
 * @description 获取popup命名空间下的文本
 * @param {string} key - 文本键
 * @param {Object} params - 参数对象
 * @returns {string} 弹窗文本
 */
function lang_getPopupText(key, params = {}) {
    return lang_getText(`popup.${key}`, params);
}

/**
 * @function lang_getTextWithFallback - 获取文本（带后备）
 * @description 获取文本，如果不存在则返回后备文本
 * @param {string} keyPath - 主要文本键路径
 * @param {string} fallbackText - 后备文本
 * @param {Object} params - 参数对象
 * @returns {string} 文本内容
 */
function lang_getTextWithFallback(keyPath, fallbackText, params = {}) {
    const text = lang_getText(keyPath, params);
    return text === keyPath ? fallbackText : text;
}

/**
 * @function lang_formatText - 格式化文本
 * @description 格式化文本，支持复数形式和条件文本
 * @param {string} keyPath - 文本键路径
 * @param {Object} params - 参数对象
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的文本
 */
function lang_formatText(keyPath, params = {}, options = {}) {
    let text = lang_getText(keyPath, params);

    // 处理复数形式
    if (options.count !== undefined) {
        text = lang_handlePlural(text, options.count);
    }

    // 处理条件文本
    if (options.condition !== undefined) {
        text = lang_handleConditional(text, options.condition);
    }

    return text;
}

/**
 * @function lang_handlePlural - 处理复数形式
 * @description 根据数量选择合适的复数形式
 * @param {string} text - 包含复数形式的文本
 * @param {number} count - 数量
 * @returns {string} 处理后的文本
 */
function lang_handlePlural(text, count) {
    // 简单的复数处理，支持 {count|singular|plural} 格式
    const pluralRegex = /\{(\d+)\|([^|]+)\|([^}]+)\}/g;
    return text.replace(pluralRegex, (match, num, singular, plural) => {
        return count === 1 ? singular : plural;
    });
}

/**
 * @function lang_handleConditional - 处理条件文本
 * @description 根据条件选择文本
 * @param {string} text - 包含条件的文本
 * @param {boolean} condition - 条件
 * @returns {string} 处理后的文本
 */
function lang_handleConditional(text, condition) {
    // 支持 {?condition|true_text|false_text} 格式
    const conditionalRegex = /\{\?[^|]*\|([^|]+)\|([^}]+)\}/g;
    return text.replace(conditionalRegex, (match, trueText, falseText) => {
        return condition ? trueText : falseText;
    });
}
// #endregion

// #region DOM更新函数
/**
 * @function lang_updateElementText - 更新元素文本
 * @description 更新DOM元素的文本内容
 * @param {string|Element} selector - CSS选择器或DOM元素
 * @param {string} keyPath - 文本键路径
 * @param {Object} params - 参数对象
 */
function lang_updateElementText(selector, keyPath, params = {}) {
    const element = typeof selector === 'string' ? document.querySelector(selector) : selector;
    if (element) {
        element.textContent = lang_getText(keyPath, params);
    }
}

/**
 * @function lang_updateElementAttribute - 更新元素属性
 * @description 更新DOM元素的属性值
 * @param {string|Element} selector - CSS选择器或DOM元素
 * @param {string} attribute - 属性名
 * @param {string} keyPath - 文本键路径
 * @param {Object} params - 参数对象
 */
function lang_updateElementAttribute(selector, attribute, keyPath, params = {}) {
    const element = typeof selector === 'string' ? document.querySelector(selector) : selector;
    if (element) {
        element.setAttribute(attribute, lang_getText(keyPath, params));
    }
}

/**
 * @function lang_updatePageLanguage - 更新页面语言
 * @description 更新页面中所有带有data-i18n属性的元素
 */
function lang_updatePageLanguage() {
    if (typeof document === 'undefined') return;

    // 更新带有data-i18n属性的元素
    const elements = document.querySelectorAll('[data-i18n]');
    elements.forEach(element => {
        const keyPath = element.getAttribute('data-i18n');
        if (keyPath) {
            const params = lang_parseElementParams(element);
            element.textContent = lang_getText(keyPath, params);
        }
    });

    // 更新带有data-i18n-html属性的元素（支持HTML内容）
    const htmlElements = document.querySelectorAll('[data-i18n-html]');
    htmlElements.forEach(element => {
        const keyPath = element.getAttribute('data-i18n-html');
        if (keyPath) {
            const params = lang_parseElementParams(element);
            element.innerHTML = lang_getText(keyPath, params);
        }
    });

    // 更新带有data-i18n-placeholder属性的元素
    const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
    placeholderElements.forEach(element => {
        const keyPath = element.getAttribute('data-i18n-placeholder');
        if (keyPath) {
            const params = lang_parseElementParams(element);
            element.setAttribute('placeholder', lang_getText(keyPath, params));
        }
    });

    // 更新带有data-i18n-title属性的元素
    const titleElements = document.querySelectorAll('[data-i18n-title]');
    titleElements.forEach(element => {
        const keyPath = element.getAttribute('data-i18n-title');
        if (keyPath) {
            const params = lang_parseElementParams(element);
            element.setAttribute('title', lang_getText(keyPath, params));
        }
    });

    // 更新带有data-i18n-value属性的元素
    const valueElements = document.querySelectorAll('[data-i18n-value]');
    valueElements.forEach(element => {
        const keyPath = element.getAttribute('data-i18n-value');
        if (keyPath) {
            const params = lang_parseElementParams(element);
            element.setAttribute('value', lang_getText(keyPath, params));
        }
    });

    // 更新带有data-i18n-aria-label属性的元素
    const ariaElements = document.querySelectorAll('[data-i18n-aria-label]');
    ariaElements.forEach(element => {
        const keyPath = element.getAttribute('data-i18n-aria-label');
        if (keyPath) {
            const params = lang_parseElementParams(element);
            element.setAttribute('aria-label', lang_getText(keyPath, params));
        }
    });

    console.log(`🔄 页面语言已更新为: ${lang_currentLanguage}`);
}

/**
 * @function lang_parseElementParams - 解析元素参数
 * @description 从元素的data-i18n-params属性解析参数
 * @param {Element} element - DOM元素
 * @returns {Object} 参数对象
 */
function lang_parseElementParams(element) {
    const paramsAttr = element.getAttribute('data-i18n-params');
    if (!paramsAttr) return {};

    try {
        return JSON.parse(paramsAttr);
    } catch (error) {
        console.warn('解析元素参数失败:', paramsAttr, error);
        return {};
    }
}

/**
 * @function lang_updateElementsInContainer - 更新容器内的元素
 * @description 更新指定容器内的所有国际化元素
 * @param {Element|string} container - 容器元素或选择器
 */
function lang_updateElementsInContainer(container) {
    if (typeof document === 'undefined') return;

    const containerElement = typeof container === 'string'
        ? document.querySelector(container)
        : container;

    if (!containerElement) return;

    // 在容器内查找并更新所有国际化元素
    const i18nSelectors = [
        '[data-i18n]',
        '[data-i18n-html]',
        '[data-i18n-placeholder]',
        '[data-i18n-title]',
        '[data-i18n-value]',
        '[data-i18n-aria-label]'
    ];

    i18nSelectors.forEach(selector => {
        const elements = containerElement.querySelectorAll(selector);
        elements.forEach(element => {
            const attribute = selector.replace(/[\[\]]/g, '');
            const keyPath = element.getAttribute(attribute);
            if (keyPath) {
                const params = lang_parseElementParams(element);
                const text = lang_getText(keyPath, params);

                switch (attribute) {
                    case 'data-i18n':
                        element.textContent = text;
                        break;
                    case 'data-i18n-html':
                        element.innerHTML = text;
                        break;
                    case 'data-i18n-placeholder':
                        element.setAttribute('placeholder', text);
                        break;
                    case 'data-i18n-title':
                        element.setAttribute('title', text);
                        break;
                    case 'data-i18n-value':
                        element.setAttribute('value', text);
                        break;
                    case 'data-i18n-aria-label':
                        element.setAttribute('aria-label', text);
                        break;
                }
            }
        });
    });
}
// #endregion

// #region 工具函数
/**
 * @function lang_getCurrentLanguage - 获取当前语言
 * @returns {string} 当前语言代码
 */
function lang_getCurrentLanguage() {
    return lang_currentLanguage;
}

/**
 * @function lang_getSupportedLanguages - 获取支持的语言列表
 * @returns {Object} 支持的语言对象
 */
function lang_getSupportedLanguages() {
    return { ...SUPPORTED_LANGUAGES };
}

/**
 * @function lang_isLanguageSupported - 检查语言是否支持
 * @param {string} language - 语言代码
 * @returns {boolean} 是否支持
 */
function lang_isLanguageSupported(language) {
    return !!SUPPORTED_LANGUAGES[language];
}

/**
 * @function lang_getLanguageName - 获取语言名称
 * @param {string} language - 语言代码
 * @returns {string} 语言名称
 */
function lang_getLanguageName(language) {
    return SUPPORTED_LANGUAGES[language] || language;
}

/**
 * @function lang_detectBrowserLanguage - 检测浏览器语言
 * @returns {string} 检测到的语言代码
 */
function lang_detectBrowserLanguage() {
    const browserLang = navigator.language || navigator.userLanguage;
    
    // 尝试精确匹配
    if (SUPPORTED_LANGUAGES[browserLang]) {
        return browserLang;
    }
    
    // 尝试语言前缀匹配
    const langPrefix = browserLang.split('-')[0];
    const matchedLang = Object.keys(SUPPORTED_LANGUAGES).find(lang => 
        lang.startsWith(langPrefix)
    );
    
    return matchedLang || 'zh_CN'; // 默认返回中文
}

/**
 * @function lang_isLoaded - 检查语言资源是否已加载
 * @returns {boolean} 是否已加载
 */
function lang_isResourceLoaded() {
    return lang_isLoaded;
}

/**
 * @function lang_getLanguageDirection - 获取语言方向
 * @param {string} language - 语言代码（可选，默认当前语言）
 * @returns {string} 语言方向 ('ltr' 或 'rtl')
 */
function lang_getLanguageDirection(language = lang_currentLanguage) {
    return LANGUAGE_DIRECTIONS[language] || 'ltr';
}

/**
 * @function lang_isRTL - 检查是否为从右到左的语言
 * @param {string} language - 语言代码（可选，默认当前语言）
 * @returns {boolean} 是否为RTL语言
 */
function lang_isRTL(language = lang_currentLanguage) {
    return lang_getLanguageDirection(language) === 'rtl';
}

/**
 * @function lang_getAvailableLanguages - 获取可用语言列表
 * @returns {Array} 可用语言数组，包含代码和名称
 */
function lang_getAvailableLanguages() {
    return Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => ({
        code,
        name,
        direction: LANGUAGE_DIRECTIONS[code] || 'ltr',
        isCurrent: code === lang_currentLanguage
    }));
}

/**
 * @function lang_preloadLanguage - 预加载语言资源
 * @description 预加载指定语言的资源，但不切换当前语言
 * @param {string} language - 语言代码
 * @returns {Promise<boolean>} 是否预加载成功
 */
async function lang_preloadLanguage(language) {
    const normalizedLang = lang_normalizeLanguageCode(language);

    if (!SUPPORTED_LANGUAGES[normalizedLang]) {
        return false;
    }

    try {
        await lang_loadResources(normalizedLang);
        console.log(`✅ 语言预加载成功: ${normalizedLang}`);
        return true;
    } catch (error) {
        console.error(`❌ 语言预加载失败: ${normalizedLang}`, error);
        return false;
    }
}

/**
 * @function lang_validateCurrentMessages - 验证当前消息完整性
 * @description 检查当前语言消息是否包含所有必需的键
 * @returns {Object} 验证结果
 */
function lang_validateCurrentMessages() {
    const requiredKeys = [
        'common.ok',
        'common.cancel',
        'common.save',
        'sidepanel.title',
        'sidepanel.welcome',
        'errors.initializationFailed'
    ];

    const missing = [];
    const present = [];

    requiredKeys.forEach(key => {
        const text = lang_getText(key);
        if (text === key) {
            missing.push(key);
        } else {
            present.push(key);
        }
    });

    return {
        isValid: missing.length === 0,
        missing,
        present,
        total: requiredKeys.length,
        coverage: (present.length / requiredKeys.length * 100).toFixed(1) + '%'
    };
}

/**
 * @function lang_createLanguageSelector - 创建语言选择器
 * @description 创建一个语言选择下拉框
 * @param {Object} options - 选项配置
 * @returns {HTMLSelectElement} 语言选择器元素
 */
function lang_createLanguageSelector(options = {}) {
    if (typeof document === 'undefined') return null;

    const {
        className = 'language-selector',
        id = 'language-selector',
        onChange = null
    } = options;

    const select = document.createElement('select');
    select.className = className;
    select.id = id;

    // 添加选项
    Object.entries(SUPPORTED_LANGUAGES).forEach(([code, name]) => {
        const option = document.createElement('option');
        option.value = code;
        option.textContent = name;
        option.selected = code === lang_currentLanguage;
        select.appendChild(option);
    });

    // 添加变更事件监听器
    select.addEventListener('change', async (event) => {
        const newLanguage = event.target.value;
        const success = await lang_setCurrentLanguage(newLanguage);

        if (!success) {
            // 如果切换失败，恢复到之前的选择
            event.target.value = lang_currentLanguage;
        }

        if (onChange && typeof onChange === 'function') {
            onChange(newLanguage, success);
        }
    });

    return select;
}
// #endregion

// #region 自动初始化
// 如果在浏览器环境中，自动初始化语言系统
if (typeof window !== 'undefined' && typeof chrome !== 'undefined') {
    // 延迟初始化，确保DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            lang_initializeLanguage();
        });
    } else {
        lang_initializeLanguage();
    }
}
// #endregion

console.log('AI Side Panel Language Manager 已加载');
