# 命名规范文档

## 命名前缀系统

### 功能模块前缀
| 前缀 | 用途 | 示例 |
|------|------|------|
| `aisp_` | AI Side Panel 主要功能 | `aisp_initialize()` |
| `content_` | 内容相关功能 | `content_capture()` |
| `template_` | 模板系统相关 | `template_show()` |
| `lang_` | 语言相关功能 | `lang_switch()` |
| `kb_` | 知识库相关 | `kb_sync()` |
| `ui_` | 用户界面相关 | `ui_render()` |
| `api_` | API接口相关 | `api_call()` |
| `storage_` | 存储相关 | `storage_save()` |
| `util_` | 工具函数 | `util_debounce()` |

## 已注册命名表

### 核心功能函数
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `aisp_initialize` | 函数 | 扩展初始化 | src/background/service-worker.js | 无 |
| `aisp_contentCapture` | 函数 | 内容捕获主函数 | src/content/content-script.js | content_* |
| `content_analyzeWithGemini` | 函数 | 内容AI分析 | src/api/gemini-api.js | api_callGemini |
| `template_showQuickReply` | 函数 | 显示快捷回复 | src/components/template-popup.js | ui_render |
| `lang_switchLanguage` | 函数 | 语言切换 | src/utils/language-manager.js | storage_save |
| `kb_syncToGoogleDrive` | 函数 | 同步到Google Drive | src/api/google-drive-api.js | api_* |
| `ui_renderMindMap` | 函数 | 渲染思维导图 | src/components/mindmap-renderer.js | 第三方库 |
| `api_callGemini` | 函数 | 调用Gemini API | src/api/gemini-api.js | 无 |
| `storage_saveTemplate` | 函数 | 保存模板 | src/utils/storage-manager.js | chrome.storage |
| `util_debounce` | 函数 | 防抖工具函数 | src/utils/common-utils.js | 无 |

### 容器和组件命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `aisp_sidePanel` | 容器 | 主侧边栏容器 | src/sidepanel/sidepanel.html | 无 |
| `aisp_chatContainer` | 容器 | 聊天界面容器 | src/sidepanel/sidepanel.html | ui_* |
| `aisp_templatePopup` | 容器 | 模板弹窗容器 | src/components/template-popup.html | template_* |
| `aisp_mindmapCanvas` | 容器 | 思维导图画布 | src/components/mindmap-renderer.html | ui_renderMindMap |

### CSS类命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `.aisp-main` | CSS类 | 主容器样式 | styles/sidepanel.css | 无 |
| `.aisp-chat` | CSS类 | 聊天区域样式 | styles/sidepanel.css | 无 |
| `.aisp-template` | CSS类 | 模板弹窗样式 | styles/template-popup.css | 无 |
| `.aisp-mindmap` | CSS类 | 思维导图样式 | styles/mindmap.css | 无 |
| `.aisp-button` | CSS类 | 按钮通用样式 | styles/common.css | 无 |

### 变量和常量命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| `AISP_CONFIG` | 常量 | 扩展配置对象 | src/utils/config.js | 无 |
| `GEMINI_API_KEY` | 常量 | Gemini API密钥 | src/api/gemini-api.js | 无 |
| `SUPPORTED_LANGUAGES` | 常量 | 支持的语言列表 | src/utils/language-manager.js | 无 |
| `DEFAULT_TEMPLATES` | 常量 | 默认模板数据 | src/utils/template-defaults.js | 无 |

## 命名规则

### 函数命名
- 使用驼峰命名法
- 动词开头，描述功能
- 包含模块前缀
- 避免缩写，保持可读性

### 变量命名
- 使用驼峰命名法
- 名词性，描述内容
- 布尔值以is/has/can开头
- 常量使用全大写+下划线

### 文件命名
- 使用短横线分隔
- 小写字母
- 描述性名称
- 包含功能模块标识

### 避免的命名
- ❌ `unified`, `mapping`, `converter`, `helper`
- ❌ `data`, `info`, `item`, `obj`
- ❌ 单字母变量（除循环计数器）
- ❌ 数字结尾的变量名
