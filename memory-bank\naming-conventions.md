# 命名规范文档

## 命名前缀系统

### 功能模块前缀
| 前缀 | 用途 | 示例 |
|------|------|------|
| `aisp_` | AI Side Panel 主要功能 | `aisp_initialize()` |
| `content_` | 内容相关功能 | `content_capture()` |
| `template_` | 模板系统相关 | `template_show()` |
| `lang_` | 语言相关功能 | `lang_switch()` |
| `kb_` | 知识库相关 | `kb_sync()` |
| `ui_` | 用户界面相关 | `ui_render()` |
| `api_` | API接口相关 | `api_call()` |
| `storage_` | 存储相关 | `storage_save()` |
| `util_` | 工具函数 | `util_debounce()` |

## 命名规则

### 函数命名
- 使用驼峰命名法
- 动词开头，描述功能
- 包含模块前缀
- 避免缩写，保持可读性

### 变量命名
- 使用驼峰命名法
- 名词性，描述内容
- 布尔值以is/has/can开头
- 常量使用全大写+下划线

### 文件命名
- 使用短横线分隔
- 小写字母
- 描述性名称
- 包含功能模块标识

### 避免的命名
- ❌ `unified`, `mapping`, `converter`, `helper`
- ❌ `data`, `info`, `item`, `obj`
- ❌ 单字母变量（除循环计数器）
- ❌ 数字结尾的变量名
