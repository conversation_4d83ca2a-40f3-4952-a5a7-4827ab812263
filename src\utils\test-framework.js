/**
 * @file AI Side Panel 测试框架
 * @description 提供功能测试、兼容性测试、用户体验测试等功能
 */

// #region 全局变量
let tf_isInitialized = false;
let tf_testResults = {
    functional: { passed: 0, failed: 0, total: 0 },
    compatibility: { passed: 0, failed: 0, total: 0 },
    performance: { passed: 0, failed: 0, total: 0 },
    userExperience: { passed: 0, failed: 0, total: 0 }
};
let tf_testSuites = new Map();
let tf_performanceOptimizer = null;

// 测试配置
const TEST_CONFIG = {
    // 功能测试配置
    FUNCTIONAL: {
        timeout: 10000,
        retryAttempts: 3,
        enableMocking: true
    },
    // 兼容性测试配置
    COMPATIBILITY: {
        chromeVersions: ['90+', '100+', '110+'],
        screenResolutions: ['1920x1080', '1366x768', '1280x720'],
        languages: ['zh_CN', 'en_US', 'ja_JP', 'ko_KR']
    },
    // 性能测试配置
    PERFORMANCE: {
        maxApiResponseTime: 5000,
        maxRenderTime: 16,
        maxMemoryUsage: 100 * 1024 * 1024, // 100MB
        minCacheHitRate: 50 // 50%
    },
    // 用户体验测试配置
    USER_EXPERIENCE: {
        maxLoadTime: 3000,
        minAccessibilityScore: 80,
        maxErrorRate: 5 // 5%
    }
};

// 测试类型
const TEST_TYPES = {
    FUNCTIONAL: 'functional',
    COMPATIBILITY: 'compatibility',
    PERFORMANCE: 'performance',
    USER_EXPERIENCE: 'userExperience'
};
// #endregion

// #region 测试套件类
/**
 * @class TestSuite - 测试套件
 * @description 单个测试套件的管理
 */
class TestSuite {
    /**
     * @function constructor - 构造函数
     * @param {string} name - 测试套件名称
     * @param {string} type - 测试类型
     */
    constructor(name, type) {
        this.name = name;
        this.type = type;
        this.tests = [];
        this.results = { passed: 0, failed: 0, total: 0 };
        this.startTime = null;
        this.endTime = null;
    }
    
    /**
     * @function addTest - 添加测试
     * @param {string} name - 测试名称
     * @param {Function} testFunction - 测试函数
     * @param {Object} options - 测试选项
     */
    addTest(name, testFunction, options = {}) {
        this.tests.push({
            name,
            testFunction,
            options: {
                timeout: TEST_CONFIG.FUNCTIONAL.timeout,
                retryAttempts: TEST_CONFIG.FUNCTIONAL.retryAttempts,
                ...options
            },
            result: null,
            error: null,
            duration: 0
        });
    }
    
    /**
     * @function run - 运行测试套件
     * @returns {Promise<Object>} 测试结果
     */
    async run() {
        console.log(`🧪 开始运行测试套件: ${this.name}`);
        this.startTime = Date.now();
        
        this.results = { passed: 0, failed: 0, total: this.tests.length };
        
        for (const test of this.tests) {
            await this.runSingleTest(test);
        }
        
        this.endTime = Date.now();
        
        const duration = this.endTime - this.startTime;
        console.log(`✅ 测试套件完成: ${this.name} (${duration}ms)`);
        console.log(`📊 结果: ${this.results.passed}通过, ${this.results.failed}失败, 总计${this.results.total}`);
        
        return this.getResults();
    }
    
    /**
     * @function runSingleTest - 运行单个测试
     * @param {Object} test - 测试对象
     */
    async runSingleTest(test) {
        const startTime = Date.now();
        let attempts = 0;
        
        while (attempts <= test.options.retryAttempts) {
            try {
                console.log(`🔬 运行测试: ${test.name} (尝试 ${attempts + 1})`);
                
                // 设置超时
                const result = await Promise.race([
                    test.testFunction(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('测试超时')), test.options.timeout)
                    )
                ]);
                
                test.result = result;
                test.duration = Date.now() - startTime;
                this.results.passed++;
                
                console.log(`✅ 测试通过: ${test.name} (${test.duration}ms)`);
                break;
                
            } catch (error) {
                attempts++;
                test.error = error;
                test.duration = Date.now() - startTime;
                
                if (attempts > test.options.retryAttempts) {
                    this.results.failed++;
                    console.error(`❌ 测试失败: ${test.name} - ${error.message}`);
                } else {
                    console.warn(`⚠️ 测试重试: ${test.name} - ${error.message}`);
                    await tf_delay(1000); // 重试前等待1秒
                }
            }
        }
    }
    
    /**
     * @function getResults - 获取测试结果
     * @returns {Object} 测试结果
     */
    getResults() {
        return {
            name: this.name,
            type: this.type,
            results: this.results,
            duration: this.endTime - this.startTime,
            tests: this.tests.map(test => ({
                name: test.name,
                passed: test.result !== null && !test.error,
                duration: test.duration,
                error: test.error ? test.error.message : null
            }))
        };
    }
}
// #endregion

// #region 初始化
/**
 * @function tf_initialize - 初始化测试框架
 * @description 初始化测试框架
 * @returns {Promise<boolean>} 是否初始化成功
 */
async function tf_initialize() {
    if (tf_isInitialized) return true;
    
    try {
        console.log('🧪 测试框架初始化中...');
        
        // 获取性能优化器
        if (typeof getPerformanceOptimizer === 'function') {
            tf_performanceOptimizer = getPerformanceOptimizer();
        }
        
        // 初始化测试套件
        tf_initializeTestSuites();
        
        tf_isInitialized = true;
        console.log('✅ 测试框架初始化完成');
        return true;
        
    } catch (error) {
        console.error('❌ 测试框架初始化失败:', error);
        return false;
    }
}

/**
 * @function tf_initializeTestSuites - 初始化测试套件
 * @description 创建预定义的测试套件
 */
function tf_initializeTestSuites() {
    // 功能测试套件
    const functionalSuite = new TestSuite('功能测试', TEST_TYPES.FUNCTIONAL);
    tf_addFunctionalTests(functionalSuite);
    tf_testSuites.set('functional', functionalSuite);
    
    // 兼容性测试套件
    const compatibilitySuite = new TestSuite('兼容性测试', TEST_TYPES.COMPATIBILITY);
    tf_addCompatibilityTests(compatibilitySuite);
    tf_testSuites.set('compatibility', compatibilitySuite);
    
    // 性能测试套件
    const performanceSuite = new TestSuite('性能测试', TEST_TYPES.PERFORMANCE);
    tf_addPerformanceTests(performanceSuite);
    tf_testSuites.set('performance', performanceSuite);
    
    // 用户体验测试套件
    const userExperienceSuite = new TestSuite('用户体验测试', TEST_TYPES.USER_EXPERIENCE);
    tf_addUserExperienceTests(userExperienceSuite);
    tf_testSuites.set('userExperience', userExperienceSuite);
}
// #endregion

// #region 功能测试
/**
 * @function tf_addFunctionalTests - 添加功能测试
 * @description 添加功能测试用例
 * @param {TestSuite} suite - 测试套件
 */
function tf_addFunctionalTests(suite) {
    // 语言管理器测试
    suite.addTest('语言管理器初始化', async () => {
        if (typeof lang_getCurrentLanguage !== 'function') {
            throw new Error('语言管理器未加载');
        }
        
        const currentLang = lang_getCurrentLanguage();
        if (!currentLang) {
            throw new Error('无法获取当前语言');
        }
        
        return { currentLanguage: currentLang };
    });
    
    // 配置管理器测试
    suite.addTest('配置管理器功能', async () => {
        if (typeof getConfigManager !== 'function') {
            throw new Error('配置管理器未加载');
        }
        
        const configManager = getConfigManager();
        await configManager.initialize();
        
        // 测试配置读写
        await configManager.setConfig('test_key', 'test_value');
        const value = await configManager.getConfig('test_key');
        
        if (value !== 'test_value') {
            throw new Error('配置读写失败');
        }
        
        return { configTest: 'passed' };
    });
    
    // API管理器测试
    suite.addTest('API管理器连接', async () => {
        if (typeof getApiManager !== 'function') {
            throw new Error('API管理器未加载');
        }
        
        const apiManager = getApiManager();
        const isReady = apiManager.isReady();
        
        if (!isReady) {
            throw new Error('API管理器未就绪');
        }
        
        return { apiReady: true };
    });
    
    // 模板管理器测试
    suite.addTest('模板管理器功能', async () => {
        if (typeof getTemplateManager !== 'function') {
            throw new Error('模板管理器未加载');
        }
        
        const templateManager = getTemplateManager();
        await templateManager.initialize();
        
        const templates = await templateManager.getTemplates();
        
        if (!Array.isArray(templates)) {
            throw new Error('模板获取失败');
        }
        
        return { templateCount: templates.length };
    });
    
    // 缓存管理器测试
    suite.addTest('缓存管理器功能', async () => {
        if (typeof getCacheManager !== 'function') {
            throw new Error('缓存管理器未加载');
        }
        
        const cacheManager = getCacheManager();
        await cacheManager.initialize();
        
        // 测试缓存读写
        cacheManager.set('api_responses', 'test_key', 'test_value');
        const value = cacheManager.get('api_responses', 'test_key');
        
        if (value !== 'test_value') {
            throw new Error('缓存读写失败');
        }
        
        return { cacheTest: 'passed' };
    });
}
// #endregion

// #region 兼容性测试
/**
 * @function tf_addCompatibilityTests - 添加兼容性测试
 * @description 添加兼容性测试用例
 * @param {TestSuite} suite - 测试套件
 */
function tf_addCompatibilityTests(suite) {
    // Chrome版本兼容性测试
    suite.addTest('Chrome版本兼容性', async () => {
        const userAgent = navigator.userAgent;
        const chromeMatch = userAgent.match(/Chrome\/(\d+)/);

        if (!chromeMatch) {
            throw new Error('不是Chrome浏览器');
        }

        const chromeVersion = parseInt(chromeMatch[1]);
        if (chromeVersion < 90) {
            throw new Error(`Chrome版本过低: ${chromeVersion}`);
        }

        return { chromeVersion };
    });

    // Chrome扩展API兼容性测试
    suite.addTest('Chrome扩展API', async () => {
        if (!chrome || !chrome.runtime) {
            throw new Error('Chrome扩展API不可用');
        }

        if (!chrome.storage || !chrome.storage.local) {
            throw new Error('Chrome Storage API不可用');
        }

        if (!chrome.identity) {
            throw new Error('Chrome Identity API不可用');
        }

        return { extensionApis: 'available' };
    });

    // 屏幕分辨率适配测试
    suite.addTest('屏幕分辨率适配', async () => {
        const width = window.innerWidth;
        const height = window.innerHeight;

        if (width < 320) {
            throw new Error(`屏幕宽度过小: ${width}px`);
        }

        // 测试侧边栏宽度适配
        const sidePanel = document.querySelector('.side-panel');
        if (sidePanel) {
            const rect = sidePanel.getBoundingClientRect();
            if (rect.width > width) {
                throw new Error('侧边栏宽度超出屏幕');
            }
        }

        return { screenSize: `${width}x${height}` };
    });

    // 多语言兼容性测试
    suite.addTest('多语言兼容性', async () => {
        const supportedLanguages = TEST_CONFIG.COMPATIBILITY.languages;
        const results = {};

        for (const lang of supportedLanguages) {
            try {
                if (typeof lang_setLanguage === 'function') {
                    await lang_setLanguage(lang);
                    results[lang] = 'supported';
                } else {
                    results[lang] = 'no_function';
                }
            } catch (error) {
                results[lang] = 'error';
            }
        }

        return { languageSupport: results };
    });
}
// #endregion

// #region 性能测试
/**
 * @function tf_addPerformanceTests - 添加性能测试
 * @description 添加性能测试用例
 * @param {TestSuite} suite - 测试套件
 */
function tf_addPerformanceTests(suite) {
    // API响应时间测试
    suite.addTest('API响应时间', async () => {
        if (!tf_performanceOptimizer) {
            throw new Error('性能优化器不可用');
        }

        const metrics = tf_performanceOptimizer.getMetrics();

        // 模拟API调用测试
        const startTime = performance.now();
        await tf_delay(100); // 模拟API调用
        const endTime = performance.now();
        const responseTime = endTime - startTime;

        if (responseTime > TEST_CONFIG.PERFORMANCE.maxApiResponseTime) {
            throw new Error(`API响应时间过长: ${responseTime}ms`);
        }

        return { responseTime: responseTime };
    });

    // 内存使用测试
    suite.addTest('内存使用', async () => {
        if (!performance.memory) {
            throw new Error('内存信息不可用');
        }

        const memoryInfo = performance.memory;
        const usedMemory = memoryInfo.usedJSHeapSize;

        if (usedMemory > TEST_CONFIG.PERFORMANCE.maxMemoryUsage) {
            throw new Error(`内存使用过高: ${tf_formatBytes(usedMemory)}`);
        }

        return {
            usedMemory: tf_formatBytes(usedMemory),
            totalMemory: tf_formatBytes(memoryInfo.totalJSHeapSize)
        };
    });

    // 渲染性能测试
    suite.addTest('渲染性能', async () => {
        const renderTimes = [];

        // 测试多次渲染
        for (let i = 0; i < 10; i++) {
            const startTime = performance.now();

            // 模拟DOM操作
            const testElement = document.createElement('div');
            testElement.innerHTML = `<p>测试内容 ${i}</p>`;
            document.body.appendChild(testElement);

            // 强制重排
            testElement.offsetHeight;

            const endTime = performance.now();
            renderTimes.push(endTime - startTime);

            // 清理
            document.body.removeChild(testElement);
        }

        const averageRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;

        if (averageRenderTime > TEST_CONFIG.PERFORMANCE.maxRenderTime) {
            throw new Error(`平均渲染时间过长: ${averageRenderTime.toFixed(2)}ms`);
        }

        return { averageRenderTime: averageRenderTime.toFixed(2) };
    });

    // 缓存命中率测试
    suite.addTest('缓存命中率', async () => {
        if (typeof getCacheManager !== 'function') {
            throw new Error('缓存管理器不可用');
        }

        const cacheManager = getCacheManager();
        const stats = cacheManager.getStats();

        if (!stats.global) {
            throw new Error('缓存统计信息不可用');
        }

        const hitRate = parseFloat(stats.global.hitRate);

        if (hitRate < TEST_CONFIG.PERFORMANCE.minCacheHitRate) {
            throw new Error(`缓存命中率过低: ${hitRate}%`);
        }

        return { cacheHitRate: stats.global.hitRate };
    });
}
// #endregion

// #region 用户体验测试
/**
 * @function tf_addUserExperienceTests - 添加用户体验测试
 * @description 添加用户体验测试用例
 * @param {TestSuite} suite - 测试套件
 */
function tf_addUserExperienceTests(suite) {
    // 页面加载时间测试
    suite.addTest('页面加载时间', async () => {
        if (!performance.timing) {
            throw new Error('性能时间信息不可用');
        }

        const timing = performance.timing;
        const loadTime = timing.loadEventEnd - timing.navigationStart;

        if (loadTime > TEST_CONFIG.USER_EXPERIENCE.maxLoadTime) {
            throw new Error(`页面加载时间过长: ${loadTime}ms`);
        }

        return { loadTime: loadTime };
    });

    // 界面响应性测试
    suite.addTest('界面响应性', async () => {
        const responseTimes = [];

        // 测试按钮点击响应
        for (let i = 0; i < 5; i++) {
            const startTime = performance.now();

            // 模拟用户交互
            const event = new Event('click');
            document.dispatchEvent(event);

            const endTime = performance.now();
            responseTimes.push(endTime - startTime);

            await tf_delay(100);
        }

        const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;

        if (averageResponseTime > 100) { // 100ms阈值
            throw new Error(`界面响应时间过长: ${averageResponseTime.toFixed(2)}ms`);
        }

        return { averageResponseTime: averageResponseTime.toFixed(2) };
    });

    // 错误率测试
    suite.addTest('错误率', async () => {
        // 模拟错误统计
        const totalOperations = 100;
        const errors = 2; // 模拟2个错误
        const errorRate = (errors / totalOperations) * 100;

        if (errorRate > TEST_CONFIG.USER_EXPERIENCE.maxErrorRate) {
            throw new Error(`错误率过高: ${errorRate}%`);
        }

        return { errorRate: errorRate };
    });

    // 可访问性测试
    suite.addTest('可访问性', async () => {
        const accessibilityIssues = [];

        // 检查图片alt属性
        const images = document.querySelectorAll('img');
        images.forEach((img, index) => {
            if (!img.alt) {
                accessibilityIssues.push(`图片${index}缺少alt属性`);
            }
        });

        // 检查按钮标签
        const buttons = document.querySelectorAll('button');
        buttons.forEach((button, index) => {
            if (!button.textContent && !button.getAttribute('aria-label')) {
                accessibilityIssues.push(`按钮${index}缺少标签`);
            }
        });

        const score = Math.max(0, 100 - accessibilityIssues.length * 10);

        if (score < TEST_CONFIG.USER_EXPERIENCE.minAccessibilityScore) {
            throw new Error(`可访问性评分过低: ${score}分`);
        }

        return {
            accessibilityScore: score,
            issues: accessibilityIssues
        };
    });
}
// #endregion

// #region 测试执行和工具函数
/**
 * @function tf_runAllTests - 运行所有测试
 * @description 运行所有测试套件
 * @returns {Promise<Object>} 测试结果
 */
async function tf_runAllTests() {
    console.log('🧪 开始运行所有测试...');
    const startTime = Date.now();

    const results = {};

    for (const [name, suite] of tf_testSuites) {
        try {
            const suiteResult = await suite.run();
            results[name] = suiteResult;

            // 更新全局统计
            tf_testResults[suite.type].passed += suiteResult.results.passed;
            tf_testResults[suite.type].failed += suiteResult.results.failed;
            tf_testResults[suite.type].total += suiteResult.results.total;

        } catch (error) {
            console.error(`测试套件运行失败: ${name}`, error);
            results[name] = {
                name: name,
                type: suite.type,
                error: error.message,
                results: { passed: 0, failed: 1, total: 1 }
            };
        }
    }

    const endTime = Date.now();
    const totalDuration = endTime - startTime;

    console.log(`✅ 所有测试完成 (${totalDuration}ms)`);

    return {
        summary: tf_testResults,
        details: results,
        duration: totalDuration,
        timestamp: new Date().toISOString()
    };
}

/**
 * @function tf_runTestSuite - 运行指定测试套件
 * @description 运行指定的测试套件
 * @param {string} suiteName - 测试套件名称
 * @returns {Promise<Object>} 测试结果
 */
async function tf_runTestSuite(suiteName) {
    const suite = tf_testSuites.get(suiteName);

    if (!suite) {
        throw new Error(`测试套件不存在: ${suiteName}`);
    }

    return await suite.run();
}

/**
 * @function tf_delay - 延迟函数
 * @description 创建指定时间的延迟
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} 延迟Promise
 */
function tf_delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * @function tf_formatBytes - 格式化字节数
 * @description 将字节数格式化为可读格式
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的字符串
 */
function tf_formatBytes(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * @function tf_generateTestReport - 生成测试报告
 * @description 生成详细的测试报告
 * @param {Object} testResults - 测试结果
 * @returns {string} 测试报告
 */
function tf_generateTestReport(testResults) {
    let report = '# AI Side Panel 测试报告\n\n';

    report += `## 测试概览\n`;
    report += `- 测试时间: ${testResults.timestamp}\n`;
    report += `- 总耗时: ${testResults.duration}ms\n\n`;

    // 汇总统计
    report += `## 测试统计\n`;
    for (const [type, stats] of Object.entries(testResults.summary)) {
        const passRate = stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(1) : 0;
        report += `- ${type}: ${stats.passed}通过/${stats.total}总计 (${passRate}%)\n`;
    }
    report += '\n';

    // 详细结果
    report += `## 详细结果\n`;
    for (const [suiteName, suiteResult] of Object.entries(testResults.details)) {
        report += `### ${suiteResult.name}\n`;
        report += `- 类型: ${suiteResult.type}\n`;
        report += `- 耗时: ${suiteResult.duration}ms\n`;

        if (suiteResult.error) {
            report += `- 错误: ${suiteResult.error}\n`;
        } else {
            report += `- 通过: ${suiteResult.results.passed}/${suiteResult.results.total}\n`;

            // 单个测试结果
            if (suiteResult.tests) {
                for (const test of suiteResult.tests) {
                    const status = test.passed ? '✅' : '❌';
                    report += `  - ${status} ${test.name} (${test.duration}ms)\n`;
                    if (test.error) {
                        report += `    错误: ${test.error}\n`;
                    }
                }
            }
        }
        report += '\n';
    }

    return report;
}

/**
 * @function getTestFramework - 获取测试框架实例
 * @description 获取全局测试框架实例
 * @returns {Object} 测试框架API对象
 */
function getTestFramework() {
    return {
        // 初始化
        initialize: tf_initialize,

        // 测试执行
        runAllTests: tf_runAllTests,
        runTestSuite: tf_runTestSuite,

        // 测试管理
        getTestSuites: () => Array.from(tf_testSuites.keys()),
        getTestResults: () => tf_testResults,

        // 工具函数
        generateTestReport: tf_generateTestReport,
        formatBytes: tf_formatBytes,

        // 常量
        TEST_CONFIG,
        TEST_TYPES
    };
}
// #endregion

console.log('🧪 AI Side Panel 测试框架已加载');
