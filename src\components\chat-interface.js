/**
 * @file AI Side Panel 聊天界面组件
 * @description 处理聊天界面的消息显示、用户交互和界面更新
 */

// #region 聊天界面类定义
/**
 * @class ui_ChatInterface - 聊天界面组件类
 * @description 管理聊天消息的显示和交互
 */
class ui_ChatInterface {
    /**
     * @constructor
     * @param {string} containerId - 聊天容器的ID
     */
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.messages = [];
        this.messageIdCounter = 0;
        
        if (!this.container) {
            console.error(`聊天容器未找到: ${containerId}`);
            return;
        }
        
        this.ui_initializeChatInterface();
    }
    
    /**
     * @function ui_initializeChatInterface - 初始化聊天界面
     * @description 设置聊天界面的基本结构和事件监听
     */
    ui_initializeChatInterface() {
        this.container.className = 'aisp-message-list';
        
        // 添加滚动事件监听
        this.container.addEventListener('scroll', util_throttle(() => {
            this.ui_handleScroll();
        }, 100));
        
        console.log('聊天界面初始化完成');
    }
    
    /**
     * @function addMessage - 添加消息
     * @description 向聊天界面添加新消息
     * @param {string} content - 消息内容
     * @param {string} type - 消息类型 ('user', 'ai', 'system')
     * @param {Object} options - 可选参数
     * @returns {string} 消息ID
     */
    addMessage(content, type = 'user', options = {}) {
        const messageId = `msg_${++this.messageIdCounter}`;
        const timestamp = new Date();
        
        const message = {
            id: messageId,
            content: content,
            type: type,
            timestamp: timestamp,
            ...options
        };
        
        this.messages.push(message);
        this.ui_renderMessage(message);
        this.ui_scrollToBottom();
        
        return messageId;
    }
    
    /**
     * @function ui_renderMessage - 渲染消息
     * @description 将消息渲染到DOM中
     * @param {Object} message - 消息对象
     */
    ui_renderMessage(message) {
        const messageElement = this.ui_createMessageElement(message);
        this.container.appendChild(messageElement);
        
        // 添加动画效果
        requestAnimationFrame(() => {
            messageElement.classList.add('aisp-message-visible');
        });
    }
    
    /**
     * @function ui_createMessageElement - 创建消息元素
     * @description 创建消息的DOM元素
     * @param {Object} message - 消息对象
     * @returns {Element} 消息DOM元素
     */
    ui_createMessageElement(message) {
        const messageDiv = util_createElement('div', {
            className: `aisp-message aisp-message-${message.type}`,
            'data-message-id': message.id
        });
        
        // 创建消息内容容器
        const contentDiv = util_createElement('div', {
            className: 'aisp-message-content'
        });
        
        // 创建消息文本
        const textDiv = util_createElement('div', {
            className: 'aisp-message-text'
        });
        
        // 根据消息类型处理内容
        if (message.type === 'system') {
            textDiv.innerHTML = this.ui_formatSystemMessage(message.content);
        } else {
            textDiv.textContent = message.content;
        }
        
        contentDiv.appendChild(textDiv);
        
        // 添加时间戳
        if (message.showTimestamp !== false) {
            const timestampDiv = util_createElement('div', {
                className: 'aisp-message-timestamp'
            }, util_getRelativeTime(message.timestamp));
            
            contentDiv.appendChild(timestampDiv);
        }
        
        // 添加操作按钮
        if (message.type === 'ai' || message.type === 'system') {
            const actionsDiv = this.ui_createMessageActions(message);
            contentDiv.appendChild(actionsDiv);
        }
        
        messageDiv.appendChild(contentDiv);
        
        return messageDiv;
    }
    
    /**
     * @function ui_formatSystemMessage - 格式化系统消息
     * @description 格式化系统消息的HTML内容
     * @param {string} content - 消息内容
     * @returns {string} 格式化后的HTML
     */
    ui_formatSystemMessage(content) {
        // 处理Markdown样式的文本
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    
    /**
     * @function ui_createMessageActions - 创建消息操作按钮
     * @description 为消息创建操作按钮（复制、重新生成等）
     * @param {Object} message - 消息对象
     * @returns {Element} 操作按钮容器
     */
    ui_createMessageActions(message) {
        const actionsDiv = util_createElement('div', {
            className: 'aisp-message-actions'
        });
        
        // 复制按钮
        const copyBtn = util_createElement('button', {
            className: 'aisp-message-action-btn aisp-copy-btn',
            title: lang_getCommonText('copy') || '复制'
        }, '📋');
        
        copyBtn.addEventListener('click', () => {
            this.ui_copyMessage(message);
        });
        
        actionsDiv.appendChild(copyBtn);
        
        // 如果是AI消息，添加重新生成按钮
        if (message.type === 'ai') {
            const regenerateBtn = util_createElement('button', {
                className: 'aisp-message-action-btn aisp-regenerate-btn',
                title: '重新生成'
            }, '🔄');
            
            regenerateBtn.addEventListener('click', () => {
                this.ui_regenerateMessage(message);
            });
            
            actionsDiv.appendChild(regenerateBtn);
        }
        
        return actionsDiv;
    }
    
    /**
     * @function ui_copyMessage - 复制消息内容
     * @description 将消息内容复制到剪贴板
     * @param {Object} message - 消息对象
     */
    async ui_copyMessage(message) {
        try {
            await navigator.clipboard.writeText(message.content);
            this.ui_showToast('已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.ui_showToast('复制失败', 'error');
        }
    }
    
    /**
     * @function ui_regenerateMessage - 重新生成消息
     * @description 重新生成AI消息
     * @param {Object} message - 消息对象
     */
    ui_regenerateMessage(message) {
        // 这里将在后续实现AI集成时完善
        this.ui_showToast('重新生成功能正在开发中', 'info');
    }
    
    /**
     * @function ui_scrollToBottom - 滚动到底部
     * @description 将聊天界面滚动到最新消息
     */
    ui_scrollToBottom() {
        if (this.container) {
            this.container.scrollTop = this.container.scrollHeight;
        }
    }
    
    /**
     * @function ui_handleScroll - 处理滚动事件
     * @description 处理聊天界面的滚动事件
     */
    ui_handleScroll() {
        // 检查是否滚动到顶部，用于加载历史消息
        if (this.container.scrollTop === 0) {
            this.ui_loadMoreMessages();
        }
    }
    
    /**
     * @function ui_loadMoreMessages - 加载更多消息
     * @description 加载历史消息（占位函数）
     */
    ui_loadMoreMessages() {
        // 这里将在后续实现消息历史功能时完善
        console.log('加载更多消息功能待实现');
    }
    
    /**
     * @function clearMessages - 清空所有消息
     * @description 清空聊天界面的所有消息
     */
    clearMessages() {
        this.messages = [];
        this.messageIdCounter = 0;
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
    
    /**
     * @function removeMessage - 删除指定消息
     * @description 删除指定ID的消息
     * @param {string} messageId - 消息ID
     */
    removeMessage(messageId) {
        // 从数组中移除
        this.messages = this.messages.filter(msg => msg.id !== messageId);
        
        // 从DOM中移除
        const messageElement = this.container.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            messageElement.remove();
        }
    }
    
    /**
     * @function updateMessage - 更新消息内容
     * @description 更新指定消息的内容
     * @param {string} messageId - 消息ID
     * @param {string} newContent - 新内容
     */
    updateMessage(messageId, newContent) {
        // 更新数组中的消息
        const message = this.messages.find(msg => msg.id === messageId);
        if (message) {
            message.content = newContent;
            
            // 更新DOM中的消息
            const messageElement = this.container.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                const textElement = messageElement.querySelector('.aisp-message-text');
                if (textElement) {
                    if (message.type === 'system') {
                        textElement.innerHTML = this.ui_formatSystemMessage(newContent);
                    } else {
                        textElement.textContent = newContent;
                    }
                }
            }
        }
    }
    
    /**
     * @function ui_showToast - 显示提示消息
     * @description 显示临时提示消息
     * @param {string} message - 提示内容
     * @param {string} type - 提示类型
     */
    ui_showToast(message, type = 'info') {
        // 创建提示元素
        const toast = util_createElement('div', {
            className: `aisp-toast aisp-toast-${type}`
        }, message);
        
        // 添加到页面
        document.body.appendChild(toast);
        
        // 显示动画
        requestAnimationFrame(() => {
            toast.classList.add('aisp-toast-visible');
        });
        
        // 自动移除
        setTimeout(() => {
            toast.classList.remove('aisp-toast-visible');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * @function getMessages - 获取所有消息
     * @description 获取当前所有消息
     * @returns {Array} 消息数组
     */
    getMessages() {
        return [...this.messages];
    }
    
    /**
     * @function getMessage - 获取指定消息
     * @description 获取指定ID的消息
     * @param {string} messageId - 消息ID
     * @returns {Object|null} 消息对象或null
     */
    getMessage(messageId) {
        return this.messages.find(msg => msg.id === messageId) || null;
    }
}
// #endregion

console.log('AI Side Panel Chat Interface 已加载');
