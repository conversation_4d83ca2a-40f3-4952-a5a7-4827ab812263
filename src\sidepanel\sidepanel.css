/**
 * AI Side Panel 侧边栏样式 - Apple Design System
 * 采用Apple Design System的设计原则，适配Chrome侧边栏规格
 */

/* #region 主容器布局 - Apple 风格 */
.aisp-main {
    display: flex;
    flex-direction: column;
    height: 100vh;
    min-width: var(--aisp-sidebar-min-width);
    max-width: var(--aisp-sidebar-max-width);
    background: var(--aisp-background-primary);
    font-family: var(--aisp-font-family);
    color: var(--aisp-label-primary);
    overflow: hidden;
    position: relative;
}

/* 毛玻璃背景效果 */
.aisp-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--aisp-blur-background);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
    backdrop-filter: var(--aisp-blur-material-thin);
    z-index: -1;
}
/* #endregion */

/* #region 头部工具栏 - Apple 风格 */
.aisp-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--aisp-spacing-4) var(--aisp-spacing-5);
    background: var(--aisp-background-secondary);
    color: var(--aisp-label-primary);
    border-bottom: 1px solid var(--aisp-separator-non-opaque);
    flex-shrink: 0;
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
    position: relative;
    z-index: 10;
}

.aisp-header-left {
    display: flex;
    align-items: center;
    flex: 1;
}

.aisp-logo {
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-3);
}

.aisp-logo-img {
    width: 28px;
    height: 28px;
    border-radius: var(--aisp-corner-radius-small);
    box-shadow: var(--aisp-shadow-1);
}

.aisp-title {
    font-size: var(--aisp-font-size-headline);
    font-weight: var(--aisp-font-weight-semibold);
    margin: 0;
    color: var(--aisp-label-primary);
    letter-spacing: -0.02em;
}

.aisp-header-right {
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-2);
}

/* Apple 风格语言选择器 */
.aisp-language-selector {
    position: relative;
}

.aisp-language-selector .aisp-select {
    background: var(--aisp-fill-tertiary);
    border: 1px solid var(--aisp-separator-non-opaque);
    color: var(--aisp-label-primary);
    padding: var(--aisp-spacing-2) var(--aisp-spacing-6) var(--aisp-spacing-2) var(--aisp-spacing-3);
    border-radius: var(--aisp-corner-radius-medium);
    font-size: var(--aisp-font-size-footnote);
    font-weight: var(--aisp-font-weight-medium);
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-language-selector .aisp-select:hover {
    background: var(--aisp-fill-secondary);
}

.aisp-language-selector .aisp-select:focus {
    border-color: var(--aisp-system-blue);
    box-shadow: 0 0 0 2px var(--aisp-system-blue-light);
}

.aisp-language-selector .aisp-select option {
    background: var(--aisp-background-tertiary);
    color: var(--aisp-label-primary);
}

/* Apple 风格图标按钮 */
.aisp-icon-btn {
    background: var(--aisp-fill-tertiary);
    border: none;
    color: var(--aisp-label-secondary);
    width: 32px;
    height: 32px;
    border-radius: var(--aisp-corner-radius-medium);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
    position: relative;
    overflow: hidden;
}

.aisp-icon-btn:hover {
    background: var(--aisp-fill-secondary);
    color: var(--aisp-label-primary);
    transform: scale(1.05);
}

.aisp-icon-btn:active {
    transform: scale(0.95);
    background: var(--aisp-fill-primary);
}

.aisp-icon {
    font-size: var(--aisp-font-size-subheadline);
    line-height: 1;
}

/* 悬浮显示的头部操作 */
.aisp-header-actions {
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-1);
    opacity: 0;
    transform: translateX(8px);
    transition: all var(--aisp-animation-duration-medium) var(--aisp-animation-easing);
}

.aisp-header:hover .aisp-header-actions {
    opacity: 1;
    transform: translateX(0);
}

/* 简约模式 - 隐藏非核心元素 */
@media (max-width: 380px) {
    .aisp-title {
        font-size: var(--aisp-font-size-callout);
    }

    .aisp-language-selector {
        display: none;
    }

    .aisp-header-actions .aisp-icon-btn:not(:first-child) {
        display: none;
    }
}
/* #endregion */

/* #region 主要内容区 - Apple 风格 */
.aisp-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--aisp-background-primary);
}

.aisp-chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.aisp-message-list {
    flex: 1;
    overflow-y: auto;
    padding: var(--aisp-spacing-5);
    display: flex;
    flex-direction: column;
    gap: var(--aisp-spacing-5);
    scroll-behavior: smooth;
}

/* Apple 风格滚动条 */
.aisp-message-list::-webkit-scrollbar {
    width: 8px;
}

.aisp-message-list::-webkit-scrollbar-track {
    background: transparent;
}

.aisp-message-list::-webkit-scrollbar-thumb {
    background: var(--aisp-fill-tertiary);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
}

.aisp-message-list::-webkit-scrollbar-thumb:hover {
    background: var(--aisp-fill-secondary);
    background-clip: content-box;
}

.aisp-message-list::-webkit-scrollbar-corner {
    background: transparent;
}

/* 滚动渐变遮罩 */
.aisp-chat-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: var(--aisp-spacing-5);
    background: linear-gradient(to bottom, var(--aisp-background-primary), transparent);
    pointer-events: none;
    z-index: 1;
}

.aisp-chat-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--aisp-spacing-5);
    background: linear-gradient(to top, var(--aisp-background-primary), transparent);
    pointer-events: none;
    z-index: 1;
}
/* #endregion */

/* #region 消息样式 - Apple 风格 */
.aisp-message {
    display: flex;
    flex-direction: column;
    margin-bottom: var(--aisp-spacing-4);
    opacity: 0;
    transform: translateY(10px);
    animation: aisp-message-appear var(--aisp-animation-duration-medium) var(--aisp-animation-easing) forwards;
}

@keyframes aisp-message-appear {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.aisp-message-visible {
    opacity: 1;
    transform: translateY(0);
}

/* Apple 风格系统消息 */
.aisp-message-system {
    background: var(--aisp-fill-quaternary);
    border-radius: var(--aisp-corner-radius-large);
    padding: var(--aisp-spacing-5);
    border: 1px solid var(--aisp-separator-non-opaque);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
    backdrop-filter: var(--aisp-blur-material-thin);
}

/* 用户消息 - 右对齐 */
.aisp-message-user {
    align-self: flex-end;
    max-width: 85%;
}

/* AI消息 - 左对齐 */
.aisp-message-ai {
    align-self: flex-start;
    max-width: 85%;
}

/* Apple 风格消息气泡 */
.aisp-message-content {
    background: var(--aisp-fill-secondary);
    padding: var(--aisp-spacing-4) var(--aisp-spacing-5);
    border-radius: var(--aisp-corner-radius-large);
    border: 1px solid var(--aisp-separator-non-opaque);
    position: relative;
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
    backdrop-filter: var(--aisp-blur-material-thin);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

/* 用户消息气泡 - Apple 蓝色 */
.aisp-message-user .aisp-message-content {
    background: var(--aisp-system-blue);
    color: white;
    border-color: var(--aisp-system-blue);
    box-shadow: var(--aisp-shadow-1);
}

/* AI消息气泡悬浮效果 */
.aisp-message-ai .aisp-message-content:hover {
    background: var(--aisp-fill-primary);
    transform: translateY(-1px);
    box-shadow: var(--aisp-shadow-2);
}

/* 消息文本样式 */
.aisp-message-text {
    line-height: 1.47059;
}

.aisp-message-text h3 {
    margin: 0 0 var(--aisp-spacing-3) 0;
    font-size: var(--aisp-font-size-headline);
    font-weight: var(--aisp-font-weight-semibold);
    color: inherit;
}

.aisp-message-text p {
    margin: 0 0 var(--aisp-spacing-3) 0;
    font-size: var(--aisp-font-size-callout);
    font-weight: var(--aisp-font-weight-regular);
    color: inherit;
}

.aisp-message-text p:last-child {
    margin-bottom: 0;
}

/* Apple 风格快速操作 */
.aisp-quick-actions {
    display: flex;
    gap: var(--aisp-spacing-3);
    margin-top: var(--aisp-spacing-4);
    flex-wrap: wrap;
}

/* 消息时间戳 */
.aisp-message-timestamp {
    font-size: var(--aisp-font-size-caption1);
    color: var(--aisp-label-tertiary);
    margin-top: var(--aisp-spacing-2);
    text-align: right;
}

.aisp-message-user .aisp-message-timestamp {
    color: rgba(255, 255, 255, 0.7);
}

/* 消息操作按钮 */
.aisp-message-actions {
    display: flex;
    gap: var(--aisp-spacing-2);
    margin-top: var(--aisp-spacing-3);
    opacity: 0;
    transform: translateY(4px);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-message:hover .aisp-message-actions {
    opacity: 1;
    transform: translateY(0);
}

.aisp-message-action-btn {
    background: var(--aisp-fill-tertiary);
    border: none;
    color: var(--aisp-label-secondary);
    padding: var(--aisp-spacing-2);
    border-radius: var(--aisp-corner-radius-small);
    cursor: pointer;
    font-size: var(--aisp-font-size-caption1);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-message-action-btn:hover {
    background: var(--aisp-fill-secondary);
    color: var(--aisp-label-primary);
    transform: scale(1.05);
}

.aisp-message-user .aisp-message-action-btn {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
}

.aisp-message-user .aisp-message-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}
/* #endregion */

/* #region 加载状态 - Apple 风格 */
.aisp-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--aisp-spacing-8);
    gap: var(--aisp-spacing-5);
    background: var(--aisp-fill-quaternary);
    border-radius: var(--aisp-corner-radius-large);
    margin: var(--aisp-spacing-5);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
    backdrop-filter: var(--aisp-blur-material-thin);
}

.aisp-loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--aisp-fill-secondary);
    border-top: 2px solid var(--aisp-system-blue);
    border-radius: 50%;
    animation: aisp-spin 1s linear infinite;
    will-change: transform;
}

@keyframes aisp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.aisp-loading-text {
    color: var(--aisp-label-secondary);
    font-size: var(--aisp-font-size-footnote);
    font-weight: var(--aisp-font-weight-medium);
    text-align: center;
}

/* Apple 风格进度指示器 */
.aisp-progress-bar {
    width: 100%;
    height: 4px;
    background: var(--aisp-fill-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin-top: var(--aisp-spacing-3);
}

.aisp-progress-fill {
    height: 100%;
    background: var(--aisp-system-blue);
    border-radius: 2px;
    transition: width var(--aisp-animation-duration-medium) var(--aisp-animation-easing);
}

.aisp-progress-indeterminate {
    width: 30%;
    animation: aisp-progress-slide 1.5s ease-in-out infinite;
}

@keyframes aisp-progress-slide {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(333%); }
}
/* #endregion */

/* #region 分析结果展示 - Apple 风格 */
.aisp-analysis-result {
    padding: var(--aisp-spacing-5);
    display: flex;
    flex-direction: column;
    gap: var(--aisp-spacing-5);
}

.aisp-result-section {
    background: var(--aisp-background-tertiary);
    border-radius: var(--aisp-corner-radius-large);
    padding: var(--aisp-spacing-5);
    border: 1px solid var(--aisp-separator-non-opaque);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
    backdrop-filter: var(--aisp-blur-material-thin);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-result-section:hover {
    background: var(--aisp-fill-quaternary);
    transform: translateY(-1px);
    box-shadow: var(--aisp-shadow-2);
}

.aisp-result-title {
    margin: 0 0 var(--aisp-spacing-4) 0;
    font-size: var(--aisp-font-size-subheadline);
    font-weight: var(--aisp-font-weight-semibold);
    color: var(--aisp-label-primary);
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-3);
    letter-spacing: -0.01em;
}

.aisp-result-content {
    color: var(--aisp-label-primary);
    line-height: 1.47059;
    font-size: var(--aisp-font-size-callout);
}

.aisp-mindmap-container {
    min-height: 200px;
    background: var(--aisp-background-primary);
    border-radius: var(--aisp-corner-radius-medium);
    border: 1px solid var(--aisp-separator-non-opaque);
    position: relative;
    overflow: hidden;
}

/* Apple 风格回复建议 */
.aisp-reply-suggestions {
    display: flex;
    flex-direction: column;
    gap: var(--aisp-spacing-3);
}

.aisp-reply-suggestion {
    background: var(--aisp-background-tertiary);
    border: 1px solid var(--aisp-separator-non-opaque);
    border-radius: var(--aisp-corner-radius-large);
    padding: var(--aisp-spacing-4);
    cursor: pointer;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
    position: relative;
    overflow: hidden;
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
    backdrop-filter: var(--aisp-blur-material-thin);
}

.aisp-reply-suggestion:hover {
    border-color: var(--aisp-system-blue);
    background: var(--aisp-system-blue-light);
    transform: translateY(-1px);
    box-shadow: var(--aisp-shadow-2);
}

.aisp-reply-suggestion:active {
    transform: scale(0.98);
}

.aisp-reply-suggestion-text {
    margin-bottom: var(--aisp-spacing-3);
    color: var(--aisp-label-primary);
    font-size: var(--aisp-font-size-callout);
    line-height: 1.47059;
}

.aisp-reply-suggestion-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    opacity: 0;
    transform: translateY(4px);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-reply-suggestion:hover .aisp-reply-suggestion-actions {
    opacity: 1;
    transform: translateY(0);
}

.aisp-reply-suggestion-lang {
    font-size: var(--aisp-font-size-caption1);
    color: var(--aisp-label-tertiary);
    font-weight: var(--aisp-font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aisp-copy-btn {
    background: var(--aisp-system-blue);
    color: white;
    border: none;
    padding: var(--aisp-spacing-2) var(--aisp-spacing-4);
    border-radius: var(--aisp-corner-radius-medium);
    font-size: var(--aisp-font-size-caption1);
    font-weight: var(--aisp-font-weight-medium);
    cursor: pointer;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
    box-shadow: var(--aisp-shadow-1);
}

.aisp-copy-btn:hover {
    background: var(--aisp-system-blue-hover);
    transform: scale(1.05);
    box-shadow: var(--aisp-shadow-2);
}

.aisp-copy-btn:active {
    transform: scale(0.95);
}

/* 关键要点列表 */
.aisp-keypoint {
    display: flex;
    align-items: flex-start;
    gap: var(--aisp-spacing-3);
    margin-bottom: var(--aisp-spacing-3);
    padding: var(--aisp-spacing-3);
    background: var(--aisp-fill-quaternary);
    border-radius: var(--aisp-corner-radius-medium);
    font-size: var(--aisp-font-size-callout);
    line-height: 1.47059;
}

.aisp-keypoint:last-child {
    margin-bottom: 0;
}

.aisp-keypoint::before {
    content: '•';
    color: var(--aisp-system-blue);
    font-weight: var(--aisp-font-weight-bold);
    font-size: var(--aisp-font-size-headline);
    line-height: 1;
    margin-top: 2px;
}
/* #endregion */

/* #region 输入区域 - Apple 风格 */
.aisp-input-area {
    flex-shrink: 0;
    padding: var(--aisp-spacing-5);
    background: var(--aisp-background-secondary);
    border-top: 1px solid var(--aisp-separator-non-opaque);
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
}

.aisp-input-container {
    display: flex;
    flex-direction: column;
    gap: var(--aisp-spacing-3);
    max-width: 100%;
}

.aisp-textarea {
    width: 100%;
    padding: var(--aisp-spacing-4);
    border: 1px solid var(--aisp-separator-opaque);
    border-radius: var(--aisp-corner-radius-large);
    font-size: var(--aisp-font-size-callout);
    font-family: var(--aisp-font-family);
    font-weight: var(--aisp-font-weight-regular);
    color: var(--aisp-label-primary);
    background: var(--aisp-background-tertiary);
    resize: vertical;
    min-height: 60px;
    max-height: 120px;
    line-height: 1.47059;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
    -webkit-appearance: none;
    appearance: none;
}

.aisp-textarea::placeholder {
    color: var(--aisp-label-tertiary);
    font-weight: var(--aisp-font-weight-regular);
}

.aisp-textarea:focus {
    outline: none;
    border-color: var(--aisp-system-blue);
    box-shadow: 0 0 0 3px var(--aisp-system-blue-light);
    background: var(--aisp-background-primary);
}

.aisp-input-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: var(--aisp-spacing-3);
}

/* Apple 风格输入增强 */
.aisp-input-enhancement {
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-2);
    opacity: 0;
    transform: translateY(4px);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-textarea:focus + .aisp-input-actions .aisp-input-enhancement {
    opacity: 1;
    transform: translateY(0);
}

.aisp-input-hint {
    font-size: var(--aisp-font-size-caption1);
    color: var(--aisp-label-tertiary);
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-1);
}

.aisp-input-hint-key {
    background: var(--aisp-fill-secondary);
    color: var(--aisp-label-secondary);
    padding: var(--aisp-spacing-1) var(--aisp-spacing-2);
    border-radius: var(--aisp-corner-radius-small);
    font-size: var(--aisp-font-size-caption2);
    font-weight: var(--aisp-font-weight-medium);
    font-family: var(--aisp-font-family-mono);
}
/* #endregion */

/* #region 底部状态栏 - Apple 风格 */
.aisp-footer {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--aisp-spacing-3) var(--aisp-spacing-5);
    background: var(--aisp-background-secondary);
    border-top: 1px solid var(--aisp-separator-non-opaque);
    font-size: var(--aisp-font-size-caption1);
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
}

.aisp-status-info {
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-5);
    flex: 1;
}

.aisp-status-item {
    display: flex;
    align-items: center;
    gap: var(--aisp-spacing-2);
    color: var(--aisp-label-secondary);
    font-weight: var(--aisp-font-weight-medium);
}

.aisp-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: relative;
}

.aisp-status-dot::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    opacity: 0.3;
    animation: aisp-status-pulse 2s ease-in-out infinite;
}

.aisp-status-connected {
    background: var(--aisp-system-green);
}

.aisp-status-connected::after {
    background: var(--aisp-system-green);
}

.aisp-status-disconnected {
    background: var(--aisp-system-red);
}

.aisp-status-disconnected::after {
    background: var(--aisp-system-red);
}

@keyframes aisp-status-pulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.1;
        transform: scale(1.2);
    }
}

.aisp-footer-actions {
    display: flex;
    gap: var(--aisp-spacing-4);
    opacity: 0;
    transform: translateX(8px);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-footer:hover .aisp-footer-actions {
    opacity: 1;
    transform: translateX(0);
}

.aisp-text-btn {
    background: none;
    border: none;
    color: var(--aisp-system-blue);
    font-size: var(--aisp-font-size-caption1);
    font-weight: var(--aisp-font-weight-medium);
    cursor: pointer;
    text-decoration: none;
    padding: var(--aisp-spacing-1) var(--aisp-spacing-2);
    border-radius: var(--aisp-corner-radius-small);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-text-btn:hover {
    color: var(--aisp-system-blue-hover);
    background: var(--aisp-system-blue-light);
}

.aisp-text-btn:active {
    transform: scale(0.95);
}

/* 简约模式 - 隐藏非核心状态信息 */
@media (max-width: 380px) {
    .aisp-status-info {
        gap: var(--aisp-spacing-3);
    }

    .aisp-status-item:not(:first-child) {
        display: none;
    }

    .aisp-footer-actions .aisp-text-btn:not(:first-child) {
        display: none;
    }
}
/* #endregion */

/* #region 模态框 - Apple 风格 */
.aisp-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    animation: aisp-modal-fade-in var(--aisp-animation-duration-medium) var(--aisp-animation-easing) forwards;
}

@keyframes aisp-modal-fade-in {
    to {
        opacity: 1;
    }
}

.aisp-modal {
    background: var(--aisp-background-tertiary);
    border: 1px solid var(--aisp-separator-non-opaque);
    border-radius: var(--aisp-corner-radius-extra-large);
    width: 90%;
    max-width: 480px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: var(--aisp-shadow-5);
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
    transform: scale(0.9) translateY(20px);
    animation: aisp-modal-slide-in var(--aisp-animation-duration-medium) var(--aisp-animation-easing) forwards;
}

@keyframes aisp-modal-slide-in {
    to {
        transform: scale(1) translateY(0);
    }
}

.aisp-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--aisp-spacing-5);
    border-bottom: 1px solid var(--aisp-separator-non-opaque);
    background: var(--aisp-fill-quaternary);
}

.aisp-modal-title {
    margin: 0;
    font-size: var(--aisp-font-size-headline);
    font-weight: var(--aisp-font-weight-semibold);
    color: var(--aisp-label-primary);
    letter-spacing: -0.01em;
}

.aisp-modal-close {
    background: var(--aisp-fill-tertiary);
    border: none;
    color: var(--aisp-label-secondary);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--aisp-corner-radius-medium);
    cursor: pointer;
    font-size: var(--aisp-font-size-title3);
    font-weight: var(--aisp-font-weight-light);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-modal-close:hover {
    background: var(--aisp-fill-secondary);
    color: var(--aisp-label-primary);
    transform: scale(1.05);
}

.aisp-modal-close:active {
    transform: scale(0.95);
}

.aisp-modal-content {
    flex: 1;
    padding: var(--aisp-spacing-5);
    overflow-y: auto;
    color: var(--aisp-label-primary);
    font-size: var(--aisp-font-size-callout);
    line-height: 1.47059;
}

.aisp-modal-content::-webkit-scrollbar {
    width: 6px;
}

.aisp-modal-content::-webkit-scrollbar-track {
    background: transparent;
}

.aisp-modal-content::-webkit-scrollbar-thumb {
    background: var(--aisp-fill-tertiary);
    border-radius: 3px;
}

.aisp-modal-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: var(--aisp-spacing-3);
    padding: var(--aisp-spacing-5);
    border-top: 1px solid var(--aisp-separator-non-opaque);
    background: var(--aisp-fill-quaternary);
}

/* 响应式模态框 */
@media (max-width: 400px) {
    .aisp-modal {
        width: 95%;
        max-height: 90vh;
        border-radius: var(--aisp-corner-radius-large);
    }

    .aisp-modal-header,
    .aisp-modal-content,
    .aisp-modal-footer {
        padding: var(--aisp-spacing-4);
    }

    .aisp-modal-title {
        font-size: var(--aisp-font-size-callout);
    }
}
/* #endregion */

/* #region 响应式设计 - Apple 风格侧边栏适配 */
/* 超窄侧边栏 (320px-360px) */
@media (max-width: 360px) {
    .aisp-header {
        padding: var(--aisp-spacing-3) var(--aisp-spacing-4);
    }

    .aisp-title {
        font-size: var(--aisp-font-size-callout);
        font-weight: var(--aisp-font-weight-medium);
    }

    .aisp-logo-img {
        width: 24px;
        height: 24px;
    }

    .aisp-message-list {
        padding: var(--aisp-spacing-4);
        gap: var(--aisp-spacing-4);
    }

    .aisp-message-content {
        padding: var(--aisp-spacing-3) var(--aisp-spacing-4);
    }

    .aisp-input-area {
        padding: var(--aisp-spacing-4);
    }

    .aisp-footer {
        padding: var(--aisp-spacing-2) var(--aisp-spacing-4);
    }

    .aisp-result-section {
        padding: var(--aisp-spacing-4);
    }

    .aisp-analysis-result {
        padding: var(--aisp-spacing-4);
        gap: var(--aisp-spacing-4);
    }

    /* 简化按钮 */
    .aisp-btn {
        padding: var(--aisp-spacing-2) var(--aisp-spacing-3);
        font-size: var(--aisp-font-size-footnote);
    }

    /* 隐藏非核心元素 */
    .aisp-header-actions .aisp-icon-btn:nth-child(n+2) {
        display: none;
    }

    .aisp-footer-actions .aisp-text-btn:nth-child(n+2) {
        display: none;
    }
}

/* 窄侧边栏 (361px-400px) */
@media (min-width: 361px) and (max-width: 400px) {
    .aisp-header {
        padding: var(--aisp-spacing-4) var(--aisp-spacing-5);
    }

    .aisp-message-list {
        padding: var(--aisp-spacing-5);
    }

    .aisp-input-area {
        padding: var(--aisp-spacing-5);
    }

    .aisp-footer {
        padding: var(--aisp-spacing-3) var(--aisp-spacing-5);
    }
}

/* 标准侧边栏 (401px-450px) */
@media (min-width: 401px) and (max-width: 450px) {
    .aisp-message-user,
    .aisp-message-ai {
        max-width: 90%;
    }

    .aisp-reply-suggestion {
        padding: var(--aisp-spacing-5);
    }
}

/* 宽侧边栏 (451px+) */
@media (min-width: 451px) {
    .aisp-message-user,
    .aisp-message-ai {
        max-width: 85%;
    }

    .aisp-header {
        padding: var(--aisp-spacing-5) var(--aisp-spacing-6);
    }

    .aisp-message-list {
        padding: var(--aisp-spacing-6);
    }

    .aisp-input-area {
        padding: var(--aisp-spacing-6);
    }

    .aisp-footer {
        padding: var(--aisp-spacing-4) var(--aisp-spacing-6);
    }
}

/* 高度适配 */
@media (max-height: 600px) {
    .aisp-message-list {
        gap: var(--aisp-spacing-3);
    }

    .aisp-message-content {
        padding: var(--aisp-spacing-3) var(--aisp-spacing-4);
    }

    .aisp-result-section {
        padding: var(--aisp-spacing-4);
    }

    .aisp-textarea {
        min-height: 44px;
        max-height: 88px;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    .aisp-message {
        animation: none;
        opacity: 1;
        transform: none;
    }

    .aisp-modal-overlay {
        animation: none;
        opacity: 1;
    }

    .aisp-modal {
        animation: none;
        transform: none;
    }

    .aisp-status-dot::after {
        animation: none;
    }

    * {
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .aisp-message-content {
        border-width: 2px;
    }

    .aisp-btn {
        border: 2px solid currentColor;
    }

    .aisp-input,
    .aisp-textarea,
    .aisp-select {
        border-width: 2px;
    }
}

/* 打印样式 */
@media print {
    .aisp-header,
    .aisp-input-area,
    .aisp-footer,
    .aisp-btn,
    .aisp-modal-overlay {
        display: none !important;
    }

    .aisp-main {
        background: white !important;
        color: black !important;
    }

    .aisp-message-content {
        background: white !important;
        color: black !important;
        border: 1px solid black !important;
        box-shadow: none !important;
    }
}
/* #endregion */
