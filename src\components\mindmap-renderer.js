/**
 * @file AI Side Panel 思维导图渲染器
 * @description 使用原生JavaScript和SVG渲染思维导图，无需外部依赖
 */

// #region 思维导图渲染器类定义
/**
 * @class ui_MindMapRenderer - 思维导图渲染器类
 * @description 渲染和管理思维导图的显示
 */
class ui_MindMapRenderer {
    /**
     * @constructor
     * @param {string} containerId - 容器元素ID
     * @param {Object} options - 配置选项
     */
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.options = {
            width: 400,
            height: 300,
            nodeRadius: 30,
            fontSize: 12,
            lineColor: '#667eea',
            nodeColor: '#f8f9fa',
            textColor: '#333',
            ...options
        };
        
        this.nodes = [];
        this.links = [];
        this.svg = null;
        
        if (!this.container) {
            console.error(`思维导图容器未找到: ${containerId}`);
            return;
        }
        
        this.ui_initializeMindMap();
    }
    
    /**
     * @function ui_initializeMindMap - 初始化思维导图
     * @description 创建SVG容器和基本结构
     */
    ui_initializeMindMap() {
        // 清空容器
        this.container.innerHTML = '';
        
        // 创建SVG元素
        this.svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        this.svg.setAttribute('width', '100%');
        this.svg.setAttribute('height', '100%');
        this.svg.setAttribute('viewBox', `0 0 ${this.options.width} ${this.options.height}`);
        this.svg.style.background = '#ffffff';
        this.svg.style.border = '1px solid #e0e0e0';
        this.svg.style.borderRadius = '4px';
        
        // 创建定义区域（用于箭头等）
        const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
        const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
        marker.setAttribute('id', 'arrowhead');
        marker.setAttribute('markerWidth', '10');
        marker.setAttribute('markerHeight', '7');
        marker.setAttribute('refX', '9');
        marker.setAttribute('refY', '3.5');
        marker.setAttribute('orient', 'auto');
        
        const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
        polygon.setAttribute('fill', this.options.lineColor);
        
        marker.appendChild(polygon);
        defs.appendChild(marker);
        this.svg.appendChild(defs);
        
        // 创建连线组
        this.linksGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        this.linksGroup.setAttribute('class', 'mindmap-links');
        this.svg.appendChild(this.linksGroup);
        
        // 创建节点组
        this.nodesGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        this.nodesGroup.setAttribute('class', 'mindmap-nodes');
        this.svg.appendChild(this.nodesGroup);
        
        this.container.appendChild(this.svg);
        
        console.log('思维导图初始化完成');
    }
    
    /**
     * @function renderMindMap - 渲染思维导图
     * @description 根据数据渲染思维导图
     * @param {Object} data - 思维导图数据
     */
    renderMindMap(data) {
        if (!data || !data.nodes) {
            this.ui_renderEmptyState();
            return;
        }
        
        this.nodes = data.nodes;
        this.links = data.links || [];
        
        // 计算节点位置
        this.ui_calculateNodePositions();
        
        // 渲染连线
        this.ui_renderLinks();
        
        // 渲染节点
        this.ui_renderNodes();
        
        console.log('思维导图渲染完成', data);
    }
    
    /**
     * @function ui_calculateNodePositions - 计算节点位置
     * @description 使用简单的层级布局算法计算节点位置
     */
    ui_calculateNodePositions() {
        if (this.nodes.length === 0) return;
        
        // 找到根节点
        const rootNode = this.nodes.find(node => node.isRoot) || this.nodes[0];
        
        // 设置根节点位置
        rootNode.x = this.options.width / 2;
        rootNode.y = this.options.height / 2;
        
        // 按层级组织节点
        const levels = this.ui_organizeLevels(rootNode);
        
        // 计算每层节点位置
        levels.forEach((levelNodes, levelIndex) => {
            if (levelIndex === 0) return; // 跳过根节点
            
            const angleStep = (2 * Math.PI) / levelNodes.length;
            const radius = 80 + levelIndex * 60;
            
            levelNodes.forEach((node, nodeIndex) => {
                const angle = angleStep * nodeIndex;
                node.x = rootNode.x + Math.cos(angle) * radius;
                node.y = rootNode.y + Math.sin(angle) * radius;
                
                // 确保节点在画布范围内
                node.x = Math.max(this.options.nodeRadius, Math.min(this.options.width - this.options.nodeRadius, node.x));
                node.y = Math.max(this.options.nodeRadius, Math.min(this.options.height - this.options.nodeRadius, node.y));
            });
        });
    }
    
    /**
     * @function ui_organizeLevels - 组织节点层级
     * @description 将节点按层级组织
     * @param {Object} rootNode - 根节点
     * @returns {Array} 层级数组
     */
    ui_organizeLevels(rootNode) {
        const levels = [[rootNode]];
        const visited = new Set([rootNode.id]);
        
        let currentLevel = 0;
        
        while (currentLevel < levels.length) {
            const nextLevel = [];
            
            levels[currentLevel].forEach(node => {
                // 找到当前节点的子节点
                this.links.forEach(link => {
                    if (link.source === node.id && !visited.has(link.target)) {
                        const targetNode = this.nodes.find(n => n.id === link.target);
                        if (targetNode) {
                            nextLevel.push(targetNode);
                            visited.add(targetNode.id);
                        }
                    }
                });
            });
            
            if (nextLevel.length > 0) {
                levels.push(nextLevel);
            }
            
            currentLevel++;
        }
        
        return levels;
    }
    
    /**
     * @function ui_renderNodes - 渲染节点
     * @description 渲染所有节点
     */
    ui_renderNodes() {
        // 清空现有节点
        this.nodesGroup.innerHTML = '';
        
        this.nodes.forEach(node => {
            this.ui_renderNode(node);
        });
    }
    
    /**
     * @function ui_renderNode - 渲染单个节点
     * @description 渲染单个思维导图节点
     * @param {Object} node - 节点数据
     */
    ui_renderNode(node) {
        // 创建节点组
        const nodeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        nodeGroup.setAttribute('class', 'mindmap-node');
        nodeGroup.setAttribute('data-node-id', node.id);
        
        // 创建节点圆圈
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('cx', node.x);
        circle.setAttribute('cy', node.y);
        circle.setAttribute('r', this.options.nodeRadius);
        circle.setAttribute('fill', node.color || this.options.nodeColor);
        circle.setAttribute('stroke', this.options.lineColor);
        circle.setAttribute('stroke-width', '2');
        circle.style.cursor = 'pointer';
        
        // 添加节点交互
        circle.addEventListener('click', () => {
            this.ui_handleNodeClick(node);
        });
        
        circle.addEventListener('mouseenter', () => {
            circle.setAttribute('fill', '#e9ecef');
        });
        
        circle.addEventListener('mouseleave', () => {
            circle.setAttribute('fill', node.color || this.options.nodeColor);
        });
        
        nodeGroup.appendChild(circle);
        
        // 创建节点文本
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', node.x);
        text.setAttribute('y', node.y + 4);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', this.options.fontSize);
        text.setAttribute('font-family', 'Arial, sans-serif');
        text.setAttribute('fill', this.options.textColor);
        text.style.pointerEvents = 'none';
        text.style.userSelect = 'none';
        
        // 处理长文本
        const maxLength = 8;
        const displayText = node.label.length > maxLength ? 
            node.label.substring(0, maxLength) + '...' : 
            node.label;
        
        text.textContent = displayText;
        
        // 添加标题提示
        const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
        title.textContent = node.label;
        nodeGroup.appendChild(title);
        
        nodeGroup.appendChild(text);
        this.nodesGroup.appendChild(nodeGroup);
    }
    
    /**
     * @function ui_renderLinks - 渲染连线
     * @description 渲染所有连线
     */
    ui_renderLinks() {
        // 清空现有连线
        this.linksGroup.innerHTML = '';
        
        this.links.forEach(link => {
            this.ui_renderLink(link);
        });
    }
    
    /**
     * @function ui_renderLink - 渲染单个连线
     * @description 渲染单个连线
     * @param {Object} link - 连线数据
     */
    ui_renderLink(link) {
        const sourceNode = this.nodes.find(n => n.id === link.source);
        const targetNode = this.nodes.find(n => n.id === link.target);
        
        if (!sourceNode || !targetNode) return;
        
        // 计算连线端点（避免与节点重叠）
        const dx = targetNode.x - sourceNode.x;
        const dy = targetNode.y - sourceNode.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance === 0) return;
        
        const unitX = dx / distance;
        const unitY = dy / distance;
        
        const startX = sourceNode.x + unitX * this.options.nodeRadius;
        const startY = sourceNode.y + unitY * this.options.nodeRadius;
        const endX = targetNode.x - unitX * this.options.nodeRadius;
        const endY = targetNode.y - unitY * this.options.nodeRadius;
        
        // 创建连线
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', startX);
        line.setAttribute('y1', startY);
        line.setAttribute('x2', endX);
        line.setAttribute('y2', endY);
        line.setAttribute('stroke', this.options.lineColor);
        line.setAttribute('stroke-width', '2');
        line.setAttribute('marker-end', 'url(#arrowhead)');
        
        this.linksGroup.appendChild(line);
    }
    
    /**
     * @function ui_handleNodeClick - 处理节点点击
     * @description 处理节点点击事件
     * @param {Object} node - 被点击的节点
     */
    ui_handleNodeClick(node) {
        console.log('节点被点击:', node);
        
        // 触发自定义事件
        const event = new CustomEvent('mindmap-node-click', {
            detail: { node: node }
        });
        this.container.dispatchEvent(event);
    }
    
    /**
     * @function ui_renderEmptyState - 渲染空状态
     * @description 当没有数据时显示空状态
     */
    ui_renderEmptyState() {
        this.svg.innerHTML = '';
        
        // 创建空状态文本
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', this.options.width / 2);
        text.setAttribute('y', this.options.height / 2);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', '14');
        text.setAttribute('font-family', 'Arial, sans-serif');
        text.setAttribute('fill', '#999');
        text.textContent = '暂无思维导图数据';
        
        this.svg.appendChild(text);
    }
    
    /**
     * @function createSampleData - 创建示例数据
     * @description 创建示例思维导图数据
     * @returns {Object} 示例数据
     */
    static createSampleData() {
        return {
            nodes: [
                { id: 'root', label: '主题', isRoot: true, color: '#667eea' },
                { id: 'node1', label: '分支1', color: '#f8f9fa' },
                { id: 'node2', label: '分支2', color: '#f8f9fa' },
                { id: 'node3', label: '分支3', color: '#f8f9fa' },
                { id: 'node1-1', label: '子分支1-1', color: '#e9ecef' },
                { id: 'node1-2', label: '子分支1-2', color: '#e9ecef' }
            ],
            links: [
                { source: 'root', target: 'node1' },
                { source: 'root', target: 'node2' },
                { source: 'root', target: 'node3' },
                { source: 'node1', target: 'node1-1' },
                { source: 'node1', target: 'node1-2' }
            ]
        };
    }
    
    /**
     * @function clear - 清空思维导图
     * @description 清空当前思维导图
     */
    clear() {
        this.nodes = [];
        this.links = [];
        this.ui_renderEmptyState();
    }
    
    /**
     * @function resize - 调整大小
     * @description 调整思维导图大小
     * @param {number} width - 新宽度
     * @param {number} height - 新高度
     */
    resize(width, height) {
        this.options.width = width;
        this.options.height = height;
        
        if (this.svg) {
            this.svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
        }
        
        // 重新计算位置并渲染
        if (this.nodes.length > 0) {
            this.ui_calculateNodePositions();
            this.ui_renderLinks();
            this.ui_renderNodes();
        }
    }
}
// #endregion

console.log('AI Side Panel MindMap Renderer 已加载');
