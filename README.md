# AI侧边栏Chrome扩展程序

## 项目概述

Chrome侧边栏扩展程序，集成AI分析、智能回复建议、快捷模板系统和云端知识库功能。

## 核心功能

- 🤖 **AI内容分析**：集成Google Gemini API，实时分析网页内容
- 💬 **智能回复建议**：基于AI分析生成多角度回复建议
- 📝 **快捷模板系统**：可自定义的回复模板，支持智能预测
- ☁️ **云端知识库**：集成Google Drive，实现数据同步
- 🌐 **多语言支持**：支持中文/英文/日文/韩文界面

## 技术栈

- Chrome Extension Manifest V3
- JavaScript ES6+, HTML5, CSS3
- Google Gemini API, Google Drive API

## 项目结构

```
AI-side-panel/
├── manifest.json           # 扩展配置
├── memory-bank/           # 项目文档
├── src/                   # 源代码
│   ├── background/        # 后台脚本
│   ├── content/          # 内容脚本
│   ├── sidepanel/        # 侧边栏页面
│   ├── components/       # 共享组件
│   ├── utils/            # 工具函数
│   └── api/              # API接口
├── styles/               # 样式文件
├── icons/                # 图标文件
└── i18n/                 # 多语言资源
```

## 开发进度 (总体完成度：90%)

### 第一阶段：基础架构 ✅ 100%
- [x] 项目结构搭建
- [x] 文档系统建立
- [x] 扩展配置文件 (manifest.json)

### 第二阶段：核心功能 ✅ 100%
- [x] 内容捕获功能 (content-script.js)
- [x] AI分析集成 (gemini-api.js)
- [x] 基础聊天界面 (sidepanel系统)

### 第三阶段：高级功能 ✅ 100%
- [x] 智能回复系统 (reply-suggestions.js)
- [x] 模板管理系统 (template-popup.js)
- [x] 云端集成 (google-drive-api.js)

### 第四阶段：优化测试 🔄 80%
- [x] 性能优化 (performance-monitor.js)
- [x] 用户体验改进 (Apple Design)
- [ ] 最终测试验证

## 命名规范

### 功能模块前缀
- `aisp_` - AI Side Panel 主要功能
- `content_` - 内容相关功能
- `template_` - 模板系统相关
- `lang_` - 语言相关功能
- `api_` - API接口相关
- `storage_` - 存储相关
- `util_` - 工具函数

## 安装使用

### 开发环境
1. 克隆项目到本地
2. 打开Chrome扩展管理页面 (`chrome://extensions/`)
3. 启用"开发者模式"
4. 加载已解压的扩展程序
5. 配置API密钥

### 使用说明
1. 点击扩展图标打开侧边栏
2. 查看AI分析结果
3. 使用快捷模板功能
4. 配置个人偏好设置

## 开发规范

- 遵循项目命名规范
- 所有注释使用中文
- 提交前运行测试
- 更新相关文档

## 许可证

MIT License