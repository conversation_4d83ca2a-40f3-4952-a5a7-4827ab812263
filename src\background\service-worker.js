/**
 * @file AI Side Panel Chrome扩展后台服务工作器
 * @description 处理扩展的后台逻辑，包括API调用、数据同步、事件监听等
 */

// #region 导入依赖脚本
try {
    // 导入API配置
    importScripts('../config/api-keys.js');

    // 导入工具脚本
    importScripts('../utils/config-manager.js');
    importScripts('../utils/gemini-api.js');
    importScripts('../utils/api-manager.js');

    console.log('✅ Service Worker 依赖脚本加载成功');
} catch (error) {
    console.error('❌ Service Worker 依赖脚本加载失败:', error);
}
// #endregion

// #region 扩展初始化
/**
 * @function aisp_initialize - 扩展初始化函数
 * @description 扩展安装或启动时的初始化操作
 */
function aisp_initialize() {
    console.log('AI Side Panel 扩展已启动');

    // 检查API密钥状态
    const keyStatus = getApiKeyStatus();
    if (keyStatus.gemini.available) {
        console.log('✅ Gemini API密钥配置正确');
    } else {
        console.warn('⚠️ Gemini API密钥需要配置:', keyStatus.gemini.status);
    }

    // 设置默认配置（移除API密钥相关配置）
    chrome.storage.local.set({
        'aisp_config': {
            currentLanguage: 'zh_CN',
            googleDriveEnabled: false,
            autoAnalysis: true,
            templateEnabled: true
        }
    });
}

// 扩展安装事件监听
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        aisp_initialize();
        
        // 打开欢迎页面
        chrome.tabs.create({
            url: chrome.runtime.getURL('src/welcome/welcome.html')
        });
    }
});

// 扩展启动事件监听
chrome.runtime.onStartup.addListener(() => {
    aisp_initialize();
});
// #endregion

// #region 侧边栏管理
/**
 * @function aisp_toggleSidePanel - 切换侧边栏显示状态
 * @param {number} tabId - 标签页ID
 * @returns {Promise<void>}
 */
async function aisp_toggleSidePanel(tabId) {
    try {
        await chrome.sidePanel.setOptions({
            tabId,
            enabled: true,
            path: 'src/sidepanel/sidepanel.html'
        });
    } catch (error) {
        console.error('切换侧边栏失败:', error);
    }
}

// 扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
    await aisp_toggleSidePanel(tab.id);
});
// #endregion

// #region 消息通信处理
/**
 * @function aisp_handleMessage - 处理来自其他脚本的消息
 * @param {Object} request - 请求对象
 * @param {Object} sender - 发送者信息
 * @param {Function} sendResponse - 响应函数
 * @returns {boolean} 是否异步响应
 */
function aisp_handleMessage(request, sender, sendResponse) {
    switch (request.action) {
        case 'content_captured':
            // 处理内容捕获消息
            aisp_processContentCapture(request.data)
                .then(result => sendResponse({ success: true, data: result }))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        case 'analyze_content':
            // 处理内容分析请求
            aisp_analyzeContent(request.content)
                .then(result => sendResponse({ success: true, data: result }))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        case 'get_templates':
            // 获取模板数据
            aisp_getTemplates()
                .then(templates => sendResponse({ success: true, data: templates }))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        case 'save_template':
            // 保存模板
            aisp_saveTemplate(request.template)
                .then(() => sendResponse({ success: true }))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        default:
            sendResponse({ success: false, error: '未知的操作类型' });
            return false;
    }
}

// 消息监听器
chrome.runtime.onMessage.addListener(aisp_handleMessage);
// #endregion

// #region 内容处理函数
/**
 * @function aisp_processContentCapture - 处理捕获的内容
 * @param {Object} contentData - 捕获的内容数据
 * @returns {Promise<Object>} 处理结果
 */
async function aisp_processContentCapture(contentData) {
    try {
        // 存储捕获的内容
        await chrome.storage.local.set({
            'latest_content': {
                url: contentData.url,
                title: contentData.title,
                text: contentData.text,
                images: contentData.images,
                timestamp: Date.now()
            }
        });
        
        // 如果启用了自动分析，则触发AI分析
        const config = await chrome.storage.local.get('aisp_config');
        if (config.aisp_config?.autoAnalysis) {
            return await aisp_analyzeContent(contentData.text);
        }
        
        return { message: '内容已捕获' };
    } catch (error) {
        console.error('处理内容捕获失败:', error);
        throw error;
    }
}

/**
 * @function aisp_analyzeContent - 分析内容
 * @param {string} content - 要分析的内容
 * @returns {Promise<Object>} 分析结果
 */
async function aisp_analyzeContent(content) {
    try {
        // 检查API密钥状态
        const keyStatus = getApiKeyStatus();
        if (!keyStatus.gemini.available) {
            console.warn('Gemini API密钥未配置，返回默认分析结果');
            return {
                summary: '请在 src/config/api-keys.js 中配置 Gemini API 密钥以启用AI分析功能',
                keyPoints: ['配置API密钥', '重新加载扩展', '开始使用AI分析'],
                suggestions: ['请联系管理员获取API密钥'],
                error: 'API密钥未配置'
            };
        }

        // 获取API管理器实例
        const apiManager = getAPIManager();
        await apiManager.initialize();

        if (!apiManager.isReady()) {
            throw new Error('API管理器未就绪');
        }

        // 使用Gemini API分析内容
        const analysisResult = await apiManager.analyzeContent({
            text: content,
            url: '',
            title: ''
        });

        return {
            summary: analysisResult.summary || '分析完成',
            keyPoints: analysisResult.keyPoints || [],
            suggestions: analysisResult.actionItems || [],
            contentType: analysisResult.contentType || 'unknown',
            sentiment: analysisResult.sentiment || 'neutral',
            confidence: analysisResult.confidence || 0.5
        };

    } catch (error) {
        console.error('内容分析失败:', error);

        // 返回友好的错误信息
        return {
            summary: '内容分析暂时不可用',
            keyPoints: ['请检查网络连接', '确认API密钥配置正确', '稍后重试'],
            suggestions: ['联系技术支持获取帮助'],
            error: error.userMessage || error.message || '未知错误'
        };
    }
}

/**
 * @function aisp_getTemplates - 获取模板数据
 * @returns {Promise<Array>} 模板列表
 */
async function aisp_getTemplates() {
    try {
        const result = await chrome.storage.local.get('aisp_templates');
        return result.aisp_templates || [];
    } catch (error) {
        console.error('获取模板失败:', error);
        throw error;
    }
}

/**
 * @function aisp_saveTemplate - 保存模板
 * @param {Object} template - 模板对象
 * @returns {Promise<void>}
 */
async function aisp_saveTemplate(template) {
    try {
        const templates = await aisp_getTemplates();
        templates.push({
            id: Date.now().toString(),
            ...template,
            createdAt: new Date().toISOString()
        });
        
        await chrome.storage.local.set({ 'aisp_templates': templates });
    } catch (error) {
        console.error('保存模板失败:', error);
        throw error;
    }
}
// #endregion

// #region 标签页事件处理
// 标签页更新事件监听
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        // 页面加载完成后，可以进行一些初始化操作
        console.log('页面加载完成:', tab.url);
    }
});

// 标签页激活事件监听
chrome.tabs.onActivated.addListener((activeInfo) => {
    console.log('标签页切换:', activeInfo.tabId);
});
// #endregion

console.log('AI Side Panel Service Worker 已加载');
