# AI侧边栏Chrome扩展 - 代码架构文档

## 项目架构概览

本文档详细描述了AI侧边栏Chrome扩展的完整代码架构，包括文件依赖关系、调用关系和功能关联。

## 📁 项目文件结构

```
AI-side-panel/
├── manifest.json                    # Chrome扩展配置文件
├── src/                            # 源代码目录
│   ├── background/                 # 后台服务
│   │   └── service-worker.js       # 后台服务工作器 (279行)
│   ├── content/                    # 内容脚本
│   │   └── content-script.js       # 页面内容捕获脚本 (500+行)
│   ├── sidepanel/                  # 侧边栏页面
│   │   ├── sidepanel.html          # 侧边栏HTML结构
│   │   ├── sidepanel.js            # 侧边栏主逻辑 (1000+行)
│   │   └── sidepanel.css           # 侧边栏样式
│   ├── popup/                      # 弹窗页面
│   │   ├── popup.html              # 弹窗HTML结构
│   │   ├── popup.js                # 弹窗逻辑
│   │   └── popup.css               # 弹窗样式
│   ├── components/                 # 共享组件
│   │   ├── chat-interface.js       # 聊天界面组件
│   │   ├── reply-suggestions.js    # 智能回复建议组件 (687行)
│   │   ├── template-popup.js       # 模板弹窗组件
│   │   ├── mindmap-renderer.js     # 思维导图渲染器
│   │   ├── knowledge-base.js       # 知识库组件
│   │   └── performance-monitor.js  # 性能监控组件
│   ├── utils/                      # 工具函数
│   │   ├── api-manager.js          # API管理器
│   │   ├── gemini-api.js           # Gemini API客户端 (1400+行)
│   │   ├── google-drive-api.js     # Google Drive API客户端
│   │   ├── language-manager.js     # 语言管理器
│   │   ├── template-manager.js     # 模板管理器
│   │   ├── cache-manager.js        # 缓存管理器
│   │   ├── config-manager.js       # 配置管理器
│   │   ├── performance-optimizer.js # 性能优化器
│   │   ├── reply-generator.js      # 回复生成器
│   │   ├── common-utils.js         # 通用工具函数
│   │   └── test-framework.js       # 测试框架
│   └── config/                     # 配置文件
│       ├── api-keys.js             # API密钥配置
│       └── api-keys.js.template    # API密钥模板
├── styles/                         # 样式文件
│   ├── common.css                  # 通用样式
│   └── content-injection.css       # 内容注入样式
├── icons/                          # 图标文件
│   ├── icon16.png                  # 16x16图标
│   ├── icon48.png                  # 48x48图标
│   └── icon128.png                 # 128x128图标
└── i18n/                           # 多语言资源
    ├── zh_CN/messages.json         # 中文语言包 (287行)
    ├── en_US/messages.json         # 英文语言包
    ├── ja_JP/messages.json         # 日文语言包
    └── ko_KR/messages.json         # 韩文语言包
```

## 🔗 核心依赖关系图

### Chrome扩展核心架构

```
manifest.json
├── background: service-worker.js
├── content_scripts: content-script.js
├── side_panel: sidepanel.html
└── action.default_popup: popup.html
```

### 文件导入依赖关系

#### service-worker.js 依赖
```javascript
// 导入配置
importScripts('../config/api-keys.js');

// 导入工具脚本
importScripts('../utils/config-manager.js');
importScripts('../utils/gemini-api.js');
importScripts('../utils/api-manager.js');
```

#### sidepanel.html 脚本加载顺序
```html
<!-- 配置脚本 -->
<script src="../config/api-keys.js"></script>

<!-- 工具脚本 -->
<script src="../utils/config-manager.js"></script>
<script src="../utils/cache-manager.js"></script>
<script src="../utils/common-utils.js"></script>
<script src="../utils/api-manager.js"></script>
<script src="../utils/gemini-api.js"></script>
<script src="../utils/google-drive-api.js"></script>
<script src="../utils/language-manager.js"></script>
<script src="../utils/template-manager.js"></script>
<script src="../utils/reply-generator.js"></script>
<script src="../utils/performance-optimizer.js"></script>

<!-- 组件脚本 -->
<script src="../components/chat-interface.js"></script>
<script src="../components/mindmap-renderer.js"></script>
<script src="../components/reply-suggestions.js"></script>
<script src="../components/template-popup.js"></script>
<script src="../components/knowledge-base.js"></script>
<script src="../components/performance-monitor.js"></script>

<!-- 主要脚本 -->
<script src="sidepanel.js"></script>
```

## 🔄 组件初始化顺序

### 1. 扩展启动流程
```
1. manifest.json 加载
2. service-worker.js 启动
3. aisp_initialize() 执行
4. 默认配置设置
5. API密钥状态检查
```

### 2. 侧边栏初始化流程
```
1. sidepanel.html 加载
2. 依赖脚本按顺序加载
3. aisp_initializeSidePanel() 执行
4. 各组件初始化：
   - aisp_initializeLanguageSystem()
   - aisp_initializeAPIManager()
   - aisp_initializeChatInterface()
   - aisp_initializeReplySuggestions()
   - aisp_initializeTemplateSystem()
   - aisp_initializeKnowledgeBase()
   - aisp_initializePerformanceSystem()
```

### 3. 内容脚本初始化流程
```
1. content-script.js 注入
2. 页面内容分析
3. 输入框检测
4. 事件监听器设置
5. 与background通信建立
```

## 📡 通信机制

### Chrome扩展内部通信

#### 1. Background ↔ Content Script
```javascript
// Content Script → Background
chrome.runtime.sendMessage({
    action: 'content_captured',
    data: contentData
});

// Background → Content Script
chrome.tabs.sendMessage(tabId, {
    action: 'analyze_result',
    data: analysisResult
});
```

#### 2. Background ↔ Side Panel
```javascript
// Side Panel → Background
chrome.runtime.sendMessage({
    action: 'analyze_content',
    content: pageContent
});

// Background → Side Panel (通过storage)
chrome.storage.local.set({
    'analysis_result': result
});
```

#### 3. Content Script ↔ Side Panel
```javascript
// 通过Background中转
Content Script → Background → Side Panel
Side Panel → Background → Content Script
```

## 🔧 API集成架构

### Gemini API集成流程
```
1. api-keys.js 提供密钥
2. gemini-api.js 创建客户端
3. api-manager.js 管理请求
4. cache-manager.js 缓存响应
5. performance-optimizer.js 优化性能
```

### Google Drive API集成流程
```
1. OAuth2认证流程
2. google-drive-api.js 处理请求
3. knowledge-base.js 管理数据
4. 数据同步和冲突解决
```

## 🎯 功能模块关联

### 内容分析功能链
```
content-script.js (捕获)
    ↓
service-worker.js (处理)
    ↓
gemini-api.js (分析)
    ↓
sidepanel.js (展示)
    ↓
chat-interface.js (交互)
```

### 智能回复功能链
```
reply-generator.js (生成)
    ↓
reply-suggestions.js (展示)
    ↓
template-manager.js (管理)
    ↓
language-manager.js (多语言)
    ↓
clipboard API (复制)
```

### 模板系统功能链
```
template-popup.js (弹窗)
    ↓
template-manager.js (管理)
    ↓
storage-manager.js (存储)
    ↓
google-drive-api.js (同步)
```

## 📊 数据流向图

### 页面分析数据流
```
网页内容
    ↓ (content-script.js)
页面数据提取
    ↓ (chrome.runtime.sendMessage)
Background处理
    ↓ (gemini-api.js)
AI分析处理
    ↓ (chrome.storage.local)
结果存储
    ↓ (sidepanel.js)
界面展示
    ↓ (chat-interface.js)
用户交互
```

### 用户操作数据流
```
用户输入
    ↓ (sidepanel.js)
事件处理
    ↓ (reply-generator.js)
回复生成
    ↓ (reply-suggestions.js)
建议展示
    ↓ (template-manager.js)
模板保存
    ↓ (google-drive-api.js)
云端同步
```

## 🔐 配置文件关联

### API配置关联
```
api-keys.js.template (模板)
    ↓
api-keys.js (实际配置)
    ↓
gemini-api.js (Gemini集成)
    ↓
google-drive-api.js (Drive集成)
    ↓
api-manager.js (统一管理)
```

### 样式配置关联
```
common.css (通用样式)
    ↓
sidepanel.css (侧边栏样式)
    ↓
content-injection.css (内容注入样式)
    ↓
各组件内联样式
```

## 🌐 多语言架构

### 语言资源加载流程
```
language-manager.js
    ↓
i18n/{language}/messages.json
    ↓
lang_loadResources()
    ↓
界面文本更新
    ↓
用户界面本地化
```

### 支持的语言
- zh_CN: 中文 (默认)
- en_US: 英文
- ja_JP: 日文
- ko_KR: 韩文

## 🚀 性能优化架构

### 缓存系统
```
cache-manager.js
├── API响应缓存
├── 分析结果缓存
├── 模板数据缓存
└── 语言资源缓存
```

### 性能监控
```
performance-monitor.js
├── API调用时间监控
├── 内存使用监控
├── 错误率统计
└── 用户体验指标
```

## 🔍 关键函数调用关系

### 核心初始化函数
```javascript
// service-worker.js
aisp_initialize()
├── aisp_setupDefaultConfig()
├── aisp_checkAPIKeys()
├── aisp_initializeEventListeners()
└── aisp_setupStorageWatchers()

// sidepanel.js
aisp_initializeSidePanel()
├── aisp_initializeLanguageSystem()
├── aisp_initializeAPIManager()
├── aisp_initializeChatInterface()
├── aisp_initializeReplySuggestions()
├── aisp_initializeTemplateSystem()
├── aisp_initializeKnowledgeBase()
└── aisp_initializePerformanceSystem()
```

### 内容分析函数链
```javascript
// content-script.js
content_capturePageContent()
├── content_extractTextContent()
├── content_detectInputFields()
├── content_analyzePageStructure()
└── content_sendToBackground()

// gemini-api.js
api_analyzeContent()
├── api_prepareRequest()
├── api_callGeminiAPI()
├── api_processResponse()
└── api_cacheResult()
```

### 回复生成函数链
```javascript
// reply-generator.js
aisp_generateReplies()
├── aisp_analyzeContext()
├── aisp_generateMultipleAngles()
├── aisp_translateToLanguages()
└── aisp_formatSuggestions()

// reply-suggestions.js
aisp_displaySuggestions()
├── aisp_createSuggestionElements()
├── aisp_attachEventListeners()
├── aisp_enableCopyFunctionality()
└── aisp_updateLanguageDisplay()
```

## 📋 事件系统架构

### Chrome扩展事件
```javascript
// service-worker.js 事件监听
chrome.runtime.onInstalled
chrome.runtime.onMessage
chrome.tabs.onUpdated
chrome.storage.onChanged

// content-script.js 事件监听
document.addEventListener('DOMContentLoaded')
window.addEventListener('beforeunload')
document.addEventListener('input')
document.addEventListener('focus')
```

### 自定义事件系统
```javascript
// 事件发布订阅模式
aisp_eventBus = {
    events: {},
    on: function(event, callback),
    emit: function(event, data),
    off: function(event, callback)
}

// 主要事件类型
'content_analyzed'     // 内容分析完成
'reply_generated'      // 回复生成完成
'template_saved'       // 模板保存完成
'language_changed'     // 语言切换
'api_error'           // API错误
'performance_warning' // 性能警告
```

## 🗄️ 数据存储架构

### Chrome Storage使用
```javascript
// chrome.storage.local (本地存储)
{
    'user_preferences': {},      // 用户偏好设置
    'template_data': [],         // 模板数据
    'analysis_cache': {},        // 分析结果缓存
    'performance_metrics': {},   // 性能指标
    'language_settings': {}      // 语言设置
}

// chrome.storage.sync (同步存储)
{
    'api_keys': {},             // API密钥
    'sync_settings': {},        // 同步设置
    'user_profile': {}          // 用户配置
}
```

### 数据管理函数
```javascript
// storage-manager.js
storage_save(key, data)
storage_load(key)
storage_remove(key)
storage_clear()
storage_sync()
```

## 🔧 错误处理架构

### 错误分类和处理
```javascript
// 错误类型定义
const ERROR_TYPES = {
    API_ERROR: 'api_error',
    NETWORK_ERROR: 'network_error',
    STORAGE_ERROR: 'storage_error',
    PERMISSION_ERROR: 'permission_error',
    VALIDATION_ERROR: 'validation_error'
};

// 错误处理流程
try {
    // 业务逻辑
} catch (error) {
    aisp_handleError(error);
}

// 统一错误处理
aisp_handleError(error)
├── aisp_logError()
├── aisp_notifyUser()
├── aisp_reportToAnalytics()
└── aisp_attemptRecovery()
```

## 🎨 UI组件架构

### 组件层次结构
```
SidePanel (主容器)
├── ChatInterface (聊天界面)
│   ├── MessageList (消息列表)
│   ├── InputArea (输入区域)
│   └── ActionButtons (操作按钮)
├── ReplySuggestions (回复建议)
│   ├── SuggestionCard (建议卡片)
│   ├── LanguageSelector (语言选择器)
│   └── CopyButton (复制按钮)
├── TemplatePopup (模板弹窗)
│   ├── TemplateList (模板列表)
│   ├── TemplateEditor (模板编辑器)
│   └── TemplateActions (模板操作)
├── MindmapRenderer (思维导图)
│   ├── NodeRenderer (节点渲染器)
│   ├── ConnectionRenderer (连接渲染器)
│   └── InteractionHandler (交互处理器)
└── PerformanceMonitor (性能监控)
    ├── MetricsDisplay (指标显示)
    ├── AlertSystem (警告系统)
    └── OptimizationSuggestions (优化建议)
```

### 组件通信模式
```javascript
// 父子组件通信
parent.aisp_initializeChild(childConfig);
child.aisp_notifyParent(eventData);

// 兄弟组件通信 (通过事件总线)
componentA.aisp_emit('data_changed', data);
componentB.aisp_on('data_changed', handleDataChange);
```

## 🔄 状态管理架构

### 全局状态管理
```javascript
// 全局状态对象
window.aispState = {
    currentLanguage: 'zh_CN',
    apiStatus: 'ready',
    analysisInProgress: false,
    userPreferences: {},
    templateData: [],
    performanceMetrics: {}
};

// 状态更新函数
aisp_updateState(key, value)
aisp_getState(key)
aisp_resetState()
aisp_subscribeToState(key, callback)
```

### 组件状态管理
```javascript
// 每个组件维护自己的状态
const componentState = {
    isVisible: false,
    isLoading: false,
    data: null,
    error: null
};
```

这个架构文档提供了项目的完整技术视图，展示了所有组件之间的关系和数据流向。
