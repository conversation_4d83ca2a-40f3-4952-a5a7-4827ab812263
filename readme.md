# AI侧边栏Chrome扩展程序

## 项目概述

一个功能丰富的Chrome侧边栏扩展程序，集成AI分析、多语言支持、智能回复建议、快捷模板系统和云端知识库功能。

## 核心功能

- 🤖 **AI内容分析**：集成Google Gemini API，实时分析网页内容
- 🌐 **多语言支持**：支持中文/英文/日文/韩文四种语言
- 💬 **智能回复建议**：基于AI分析生成多角度回复建议
- 📝 **快捷模板系统**：可自定义的回复模板，支持智能预测
- ☁️ **云端知识库**：集成Google Drive，实现数据同步和知识管理
- 🧠 **思维导图**：可视化展示分析结果
- 💾 **本地存储**：离线模板功能，数据持久化

## 技术栈

- **Chrome Extension Manifest V3**
- **JavaScript ES6+**
- **HTML5 & CSS3**
- **Google Gemini API**
- **Google Drive API**
- **Chrome Storage API**

## 项目结构

```
AI-side-panel/
├── manifest.json                 # 扩展配置文件
├── README.md                     # 项目说明
├── memory-bank/                  # 项目管理文档
│   ├── projectbrief.md          # 项目概述
│   ├── activeContext.md         # 当前工作重点
│   ├── progress.md              # 进度跟踪
│   ├── systemPatterns.md        # 架构模式
│   ├── techContext.md           # 技术环境
│   └── naming-conventions.md    # 命名规范
├── src/                         # 源代码目录
│   ├── background/              # 后台脚本
│   │   └── service-worker.js    # 后台服务工作器
│   ├── content/                 # 内容脚本
│   │   └── content-script.js    # 页面内容捕获脚本
│   ├── sidepanel/              # 侧边栏页面
│   │   ├── sidepanel.html      # 侧边栏HTML
│   │   ├── sidepanel.js        # 侧边栏逻辑
│   │   └── sidepanel.css       # 侧边栏样式
│   ├── components/             # 共享组件
│   │   ├── chat-interface.js   # 聊天界面组件
│   │   ├── mindmap-renderer.js # 思维导图渲染器
│   │   └── template-popup.js   # 模板弹窗组件
│   ├── utils/                  # 工具函数
│   │   ├── language-manager.js # 语言管理器
│   │   ├── storage-manager.js  # 存储管理器
│   │   └── common-utils.js     # 通用工具函数
│   └── api/                    # API接口
│       ├── gemini-api.js       # Gemini API客户端
│       └── google-drive-api.js # Google Drive API客户端
├── styles/                     # 样式文件
│   ├── common.css              # 通用样式
│   ├── sidepanel.css          # 侧边栏样式
│   └── template-popup.css     # 模板弹窗样式
├── icons/                      # 图标文件
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
└── i18n/                       # 多语言资源
    ├── zh/                     # 中文
    ├── en/                     # 英文
    ├── ja/                     # 日文
    └── ko/                     # 韩文
```

## 开发阶段

### 第一阶段：基础架构搭建 ✅

- [X] 创建项目结构和配置文件
- [X] 设置manifest.json (Manifest V3)
- [X] 创建基础的侧边栏页面
- [X] 建立memory-bank文档系统
- [X] 设置命名规范

### 第二阶段：核心功能开发 🔄

- [ ] 内容捕获功能（content script）
- [ ] 基础的AI分析集成（Gemini API）
- [ ] 简单的聊天界面
- [ ] 多语言支持基础框架

### 第三阶段：高级功能实现 ⏳

- [ ] 思维导图可视化
- [ ] 智能回复建议系统
- [ ] 快捷回复模板系统
- [ ] 本地存储管理

### 第四阶段：云端集成 ⏳

- [ ] Google Drive API集成
- [ ] 知识库系统
- [ ] 数据同步功能
- [ ] 标签系统

### 第五阶段：优化与测试 ⏳

- [ ] 性能优化
- [ ] 用户体验改进
- [ ] 错误处理完善
- [ ] 全面测试

## 命名规范快速参考

### 功能模块前缀

- `aisp_` - AI Side Panel 主要功能
- `content_` - 内容相关功能
- `template_` - 模板系统相关
- `lang_` - 语言相关功能
- `kb_` - 知识库相关
- `ui_` - 用户界面相关
- `api_` - API接口相关
- `storage_` - 存储相关
- `util_` - 工具函数

## 安装和使用

### 开发环境设置

1. 克隆项目到本地
2. 在Chrome中打开扩展管理页面 (`chrome://extensions/`)
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"，选择项目目录
5. 配置API密钥（Gemini API、Google Drive API）

### 使用说明

1. 安装扩展后，点击Chrome工具栏中的扩展图标
2. 在侧边栏中查看AI分析结果
3. 使用快捷模板功能提高回复效率
4. 通过设置页面配置个人偏好

## 贡献指南

1. 遵循项目命名规范
2. 所有注释使用中文
3. 提交前运行测试
4. 更新相关文档

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或Pull Request。
