/**
 * AI Side Panel 通用样式 - Apple Design System
 * 采用Apple Design System的设计原则和规范
 */

/* #region CSS变量定义 - Apple Design System */
:root {
    /* Apple 系统色彩 - 浅色主题 */
    --aisp-system-blue: #007AFF;
    --aisp-system-blue-hover: #0056CC;
    --aisp-system-blue-light: rgba(0, 122, 255, 0.1);
    --aisp-system-gray: #8E8E93;
    --aisp-system-gray2: #AEAEB2;
    --aisp-system-gray3: #C7C7CC;
    --aisp-system-gray4: #D1D1D6;
    --aisp-system-gray5: #E5E5EA;
    --aisp-system-gray6: #F2F2F7;

    /* Apple 语义色彩 */
    --aisp-label-primary: #000000;
    --aisp-label-secondary: rgba(60, 60, 67, 0.6);
    --aisp-label-tertiary: rgba(60, 60, 67, 0.3);
    --aisp-label-quaternary: rgba(60, 60, 67, 0.18);

    --aisp-fill-primary: rgba(120, 120, 128, 0.2);
    --aisp-fill-secondary: rgba(120, 120, 128, 0.16);
    --aisp-fill-tertiary: rgba(118, 118, 128, 0.12);
    --aisp-fill-quaternary: rgba(116, 116, 128, 0.08);

    --aisp-background-primary: #FFFFFF;
    --aisp-background-secondary: #F2F2F7;
    --aisp-background-tertiary: #FFFFFF;

    --aisp-separator-opaque: #C6C6C8;
    --aisp-separator-non-opaque: rgba(60, 60, 67, 0.36);

    /* Apple 状态色彩 */
    --aisp-system-green: #34C759;
    --aisp-system-yellow: #FFCC00;
    --aisp-system-orange: #FF9500;
    --aisp-system-red: #FF3B30;
    --aisp-system-purple: #AF52DE;
    --aisp-system-pink: #FF2D92;
    --aisp-system-indigo: #5856D6;
    --aisp-system-teal: #5AC8FA;

    /* SF Pro 字体系列 */
    --aisp-font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
    --aisp-font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Apple 字体大小规范 */
    --aisp-font-size-caption2: 11px;
    --aisp-font-size-caption1: 12px;
    --aisp-font-size-footnote: 13px;
    --aisp-font-size-subheadline: 15px;
    --aisp-font-size-callout: 16px;
    --aisp-font-size-body: 17px;
    --aisp-font-size-headline: 17px;
    --aisp-font-size-title3: 20px;
    --aisp-font-size-title2: 22px;
    --aisp-font-size-title1: 28px;
    --aisp-font-size-large-title: 34px;

    /* Apple 字重 */
    --aisp-font-weight-ultralight: 100;
    --aisp-font-weight-thin: 200;
    --aisp-font-weight-light: 300;
    --aisp-font-weight-regular: 400;
    --aisp-font-weight-medium: 500;
    --aisp-font-weight-semibold: 600;
    --aisp-font-weight-bold: 700;
    --aisp-font-weight-heavy: 800;
    --aisp-font-weight-black: 900;

    /* Apple 间距系统 (8pt grid) */
    --aisp-spacing-1: 2px;
    --aisp-spacing-2: 4px;
    --aisp-spacing-3: 8px;
    --aisp-spacing-4: 12px;
    --aisp-spacing-5: 16px;
    --aisp-spacing-6: 20px;
    --aisp-spacing-7: 24px;
    --aisp-spacing-8: 32px;
    --aisp-spacing-9: 40px;
    --aisp-spacing-10: 48px;

    /* Apple 圆角规范 */
    --aisp-corner-radius-small: 6px;
    --aisp-corner-radius-medium: 8px;
    --aisp-corner-radius-large: 12px;
    --aisp-corner-radius-extra-large: 16px;
    --aisp-corner-radius-continuous: 20px;

    /* Apple 阴影系统 */
    --aisp-shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --aisp-shadow-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --aisp-shadow-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
    --aisp-shadow-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
    --aisp-shadow-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);

    /* Apple 毛玻璃效果 */
    --aisp-blur-background: rgba(255, 255, 255, 0.8);
    --aisp-blur-background-prominent: rgba(255, 255, 255, 0.9);
    --aisp-blur-material: blur(20px);
    --aisp-blur-material-thin: blur(10px);

    /* Apple 动画时间 */
    --aisp-animation-duration-short: 0.2s;
    --aisp-animation-duration-medium: 0.35s;
    --aisp-animation-duration-long: 0.5s;
    --aisp-animation-easing: cubic-bezier(0.25, 0.1, 0.25, 1);
    --aisp-animation-easing-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* 侧边栏适配尺寸 */
    --aisp-sidebar-min-width: 320px;
    --aisp-sidebar-max-width: 500px;
    --aisp-sidebar-padding: var(--aisp-spacing-5);
    --aisp-sidebar-content-max-width: calc(var(--aisp-sidebar-max-width) - 2 * var(--aisp-sidebar-padding));
}
}

/* #region 深色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        /* Apple 系统色彩 - 深色主题 */
        --aisp-system-blue: #0A84FF;
        --aisp-system-blue-hover: #409CFF;
        --aisp-system-blue-light: rgba(10, 132, 255, 0.16);
        --aisp-system-gray: #8E8E93;
        --aisp-system-gray2: #636366;
        --aisp-system-gray3: #48484A;
        --aisp-system-gray4: #3A3A3C;
        --aisp-system-gray5: #2C2C2E;
        --aisp-system-gray6: #1C1C1E;

        /* Apple 语义色彩 - 深色主题 */
        --aisp-label-primary: #FFFFFF;
        --aisp-label-secondary: rgba(235, 235, 245, 0.6);
        --aisp-label-tertiary: rgba(235, 235, 245, 0.3);
        --aisp-label-quaternary: rgba(235, 235, 245, 0.18);

        --aisp-fill-primary: rgba(120, 120, 128, 0.36);
        --aisp-fill-secondary: rgba(120, 120, 128, 0.32);
        --aisp-fill-tertiary: rgba(118, 118, 128, 0.28);
        --aisp-fill-quaternary: rgba(116, 116, 128, 0.24);

        --aisp-background-primary: #000000;
        --aisp-background-secondary: #1C1C1E;
        --aisp-background-tertiary: #2C2C2E;

        --aisp-separator-opaque: #38383A;
        --aisp-separator-non-opaque: rgba(84, 84, 88, 0.65);

        /* 深色主题状态色彩 */
        --aisp-system-green: #30D158;
        --aisp-system-yellow: #FFD60A;
        --aisp-system-orange: #FF9F0A;
        --aisp-system-red: #FF453A;
        --aisp-system-purple: #BF5AF2;
        --aisp-system-pink: #FF375F;
        --aisp-system-indigo: #5E5CE6;
        --aisp-system-teal: #64D2FF;

        /* 深色主题毛玻璃效果 */
        --aisp-blur-background: rgba(28, 28, 30, 0.8);
        --aisp-blur-background-prominent: rgba(28, 28, 30, 0.9);
    }
}
/* #endregion */

/* #region 基础重置 - Apple 风格 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.47059; /* Apple 标准行高 */
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: var(--aisp-font-family);
    font-size: var(--aisp-font-size-body);
    font-weight: var(--aisp-font-weight-regular);
    color: var(--aisp-label-primary);
    background-color: var(--aisp-background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    overflow-x: hidden;
}

/* Apple 风格的焦点样式 */
*:focus {
    outline: none;
}

*:focus-visible {
    outline: 2px solid var(--aisp-system-blue);
    outline-offset: 2px;
    border-radius: var(--aisp-corner-radius-small);
}

/* 选择文本样式 */
::selection {
    background-color: var(--aisp-system-blue-light);
    color: var(--aisp-label-primary);
}

::-moz-selection {
    background-color: var(--aisp-system-blue-light);
    color: var(--aisp-label-primary);
}

/* 滚动条样式 - Apple 风格 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--aisp-fill-tertiary);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--aisp-fill-secondary);
    background-clip: content-box;
}

::-webkit-scrollbar-corner {
    background: transparent;
}
/* #endregion */

/* #region Apple 风格按钮系统 */
.aisp-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--aisp-spacing-2);
    padding: var(--aisp-spacing-3) var(--aisp-spacing-5);
    border: none;
    border-radius: var(--aisp-corner-radius-medium);
    font-family: var(--aisp-font-family);
    font-size: var(--aisp-font-size-callout);
    font-weight: var(--aisp-font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
    -webkit-user-select: none;
    user-select: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    backdrop-filter: var(--aisp-blur-material-thin);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
}

.aisp-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none !important;
}

.aisp-btn:active:not(:disabled) {
    transform: scale(0.96);
}

/* Apple 主要按钮 - 蓝色系统色 */
.aisp-btn-primary {
    background: var(--aisp-system-blue);
    color: white;
    box-shadow: var(--aisp-shadow-1);
}

.aisp-btn-primary:hover:not(:disabled) {
    background: var(--aisp-system-blue-hover);
    box-shadow: var(--aisp-shadow-2);
    transform: translateY(-1px);
}

.aisp-btn-primary:active:not(:disabled) {
    background: var(--aisp-system-blue-hover);
    transform: scale(0.96);
    box-shadow: var(--aisp-shadow-1);
}

/* Apple 次要按钮 - 填充样式 */
.aisp-btn-secondary {
    background: var(--aisp-fill-secondary);
    color: var(--aisp-label-primary);
    backdrop-filter: var(--aisp-blur-material-thin);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
}

.aisp-btn-secondary:hover:not(:disabled) {
    background: var(--aisp-fill-primary);
    transform: translateY(-0.5px);
}

/* Apple 文本按钮 */
.aisp-btn-text {
    background: transparent;
    color: var(--aisp-system-blue);
    padding: var(--aisp-spacing-2) var(--aisp-spacing-3);
    border-radius: var(--aisp-corner-radius-small);
}

.aisp-btn-text:hover:not(:disabled) {
    background: var(--aisp-system-blue-light);
}

/* Apple 成功按钮 */
.aisp-btn-success {
    background: var(--aisp-system-green);
    color: white;
    box-shadow: var(--aisp-shadow-1);
}

.aisp-btn-success:hover:not(:disabled) {
    filter: brightness(1.1);
    box-shadow: var(--aisp-shadow-2);
    transform: translateY(-1px);
}

/* Apple 危险按钮 */
.aisp-btn-danger {
    background: var(--aisp-system-red);
    color: white;
    box-shadow: var(--aisp-shadow-1);
}

.aisp-btn-danger:hover:not(:disabled) {
    filter: brightness(1.1);
    box-shadow: var(--aisp-shadow-2);
    transform: translateY(-1px);
}

/* Apple 按钮尺寸 */
.aisp-btn-small {
    padding: var(--aisp-spacing-2) var(--aisp-spacing-4);
    font-size: var(--aisp-font-size-footnote);
    border-radius: var(--aisp-corner-radius-small);
}

.aisp-btn-large {
    padding: var(--aisp-spacing-4) var(--aisp-spacing-6);
    font-size: var(--aisp-font-size-headline);
    border-radius: var(--aisp-corner-radius-large);
}

/* Apple 图标按钮 */
.aisp-btn-icon-only {
    width: 32px;
    height: 32px;
    padding: 0;
    border-radius: var(--aisp-corner-radius-medium);
    background: var(--aisp-fill-tertiary);
    color: var(--aisp-label-secondary);
}

.aisp-btn-icon-only:hover:not(:disabled) {
    background: var(--aisp-fill-secondary);
    color: var(--aisp-label-primary);
}

/* 按钮图标 */
.aisp-btn-icon {
    font-size: 1em;
    line-height: 1;
}

/* 悬浮显示的操作按钮 */
.aisp-btn-hover-reveal {
    opacity: 0;
    transform: scale(0.8);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-hover-container:hover .aisp-btn-hover-reveal {
    opacity: 1;
    transform: scale(1);
}
/* #endregion */

/* #region Apple 风格表单控件 */
.aisp-input,
.aisp-textarea,
.aisp-select {
    width: 100%;
    padding: var(--aisp-spacing-4) var(--aisp-spacing-4);
    border: 1px solid var(--aisp-separator-opaque);
    border-radius: var(--aisp-corner-radius-medium);
    font-family: var(--aisp-font-family);
    font-size: var(--aisp-font-size-callout);
    font-weight: var(--aisp-font-weight-regular);
    color: var(--aisp-label-primary);
    background: var(--aisp-background-tertiary);
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
    -webkit-appearance: none;
    appearance: none;
}

.aisp-input::placeholder,
.aisp-textarea::placeholder {
    color: var(--aisp-label-tertiary);
    font-weight: var(--aisp-font-weight-regular);
}

.aisp-input:focus,
.aisp-textarea:focus,
.aisp-select:focus {
    border-color: var(--aisp-system-blue);
    box-shadow: 0 0 0 3px var(--aisp-system-blue-light);
    outline: none;
}

.aisp-input:disabled,
.aisp-textarea:disabled,
.aisp-select:disabled {
    background: var(--aisp-fill-quaternary);
    color: var(--aisp-label-quaternary);
    border-color: var(--aisp-separator-non-opaque);
    cursor: not-allowed;
    opacity: 0.6;
}

.aisp-textarea {
    resize: vertical;
    min-height: 80px;
    line-height: 1.47059;
}

.aisp-select {
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
    background-position: right var(--aisp-spacing-4) center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: var(--aisp-spacing-8);
}

/* Apple 风格输入组 */
.aisp-input-group {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--aisp-spacing-2);
}

.aisp-input-label {
    font-size: var(--aisp-font-size-footnote);
    font-weight: var(--aisp-font-weight-medium);
    color: var(--aisp-label-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aisp-input-helper {
    font-size: var(--aisp-font-size-caption1);
    color: var(--aisp-label-tertiary);
    margin-top: var(--aisp-spacing-1);
}

.aisp-input-error {
    color: var(--aisp-system-red);
}

/* Apple 风格搜索框 */
.aisp-search-input {
    background: var(--aisp-fill-secondary);
    border: none;
    border-radius: var(--aisp-corner-radius-large);
    padding: var(--aisp-spacing-3) var(--aisp-spacing-5);
    padding-left: var(--aisp-spacing-8);
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3E%3Cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'/%3E%3C/svg%3E");
    background-position: left var(--aisp-spacing-3) center;
    background-repeat: no-repeat;
    background-size: 16px;
}

.aisp-search-input:focus {
    background: var(--aisp-background-tertiary);
    border: 1px solid var(--aisp-system-blue);
}
/* #endregion */

/* #region Apple 风格卡片组件 */
.aisp-card {
    background: var(--aisp-background-tertiary);
    border: 1px solid var(--aisp-separator-non-opaque);
    border-radius: var(--aisp-corner-radius-large);
    box-shadow: var(--aisp-shadow-1);
    overflow: hidden;
    backdrop-filter: var(--aisp-blur-material-thin);
    -webkit-backdrop-filter: var(--aisp-blur-material-thin);
}

.aisp-card-header {
    padding: var(--aisp-spacing-5);
    border-bottom: 1px solid var(--aisp-separator-non-opaque);
    background: var(--aisp-fill-quaternary);
}

.aisp-card-body {
    padding: var(--aisp-spacing-5);
}

.aisp-card-footer {
    padding: var(--aisp-spacing-5);
    border-top: 1px solid var(--aisp-separator-non-opaque);
    background: var(--aisp-fill-quaternary);
}

/* Apple 风格列表卡片 */
.aisp-list-card {
    background: var(--aisp-background-tertiary);
    border-radius: var(--aisp-corner-radius-large);
    overflow: hidden;
    border: 1px solid var(--aisp-separator-non-opaque);
}

.aisp-list-item {
    padding: var(--aisp-spacing-4) var(--aisp-spacing-5);
    border-bottom: 1px solid var(--aisp-separator-non-opaque);
    transition: background-color var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-list-item:last-child {
    border-bottom: none;
}

.aisp-list-item:hover {
    background: var(--aisp-fill-quaternary);
}

.aisp-list-item-active {
    background: var(--aisp-system-blue-light);
}

/* Apple 风格分组 */
.aisp-group {
    background: var(--aisp-background-secondary);
    border-radius: var(--aisp-corner-radius-large);
    padding: var(--aisp-spacing-1);
    display: flex;
    gap: var(--aisp-spacing-1);
}

.aisp-group-item {
    flex: 1;
    padding: var(--aisp-spacing-3) var(--aisp-spacing-4);
    border-radius: var(--aisp-corner-radius-medium);
    text-align: center;
    font-size: var(--aisp-font-size-footnote);
    font-weight: var(--aisp-font-weight-medium);
    color: var(--aisp-label-secondary);
    cursor: pointer;
    transition: all var(--aisp-animation-duration-short) var(--aisp-animation-easing);
}

.aisp-group-item:hover {
    color: var(--aisp-label-primary);
}

.aisp-group-item-active {
    background: var(--aisp-background-tertiary);
    color: var(--aisp-label-primary);
    box-shadow: var(--aisp-shadow-1);
}
/* #endregion */

/* #region Apple 风格工具类 */
/* 文本对齐 */
.aisp-text-left { text-align: left; }
.aisp-text-center { text-align: center; }
.aisp-text-right { text-align: right; }

/* Apple 语义文本颜色 */
.aisp-text-primary { color: var(--aisp-label-primary); }
.aisp-text-secondary { color: var(--aisp-label-secondary); }
.aisp-text-tertiary { color: var(--aisp-label-tertiary); }
.aisp-text-quaternary { color: var(--aisp-label-quaternary); }

/* Apple 系统颜色 */
.aisp-text-blue { color: var(--aisp-system-blue); }
.aisp-text-green { color: var(--aisp-system-green); }
.aisp-text-yellow { color: var(--aisp-system-yellow); }
.aisp-text-orange { color: var(--aisp-system-orange); }
.aisp-text-red { color: var(--aisp-system-red); }
.aisp-text-purple { color: var(--aisp-system-purple); }
.aisp-text-pink { color: var(--aisp-system-pink); }
.aisp-text-indigo { color: var(--aisp-system-indigo); }
.aisp-text-teal { color: var(--aisp-system-teal); }

/* Apple 字体大小 */
.aisp-text-caption2 { font-size: var(--aisp-font-size-caption2); }
.aisp-text-caption1 { font-size: var(--aisp-font-size-caption1); }
.aisp-text-footnote { font-size: var(--aisp-font-size-footnote); }
.aisp-text-subheadline { font-size: var(--aisp-font-size-subheadline); }
.aisp-text-callout { font-size: var(--aisp-font-size-callout); }
.aisp-text-body { font-size: var(--aisp-font-size-body); }
.aisp-text-headline { font-size: var(--aisp-font-size-headline); }
.aisp-text-title3 { font-size: var(--aisp-font-size-title3); }
.aisp-text-title2 { font-size: var(--aisp-font-size-title2); }
.aisp-text-title1 { font-size: var(--aisp-font-size-title1); }
.aisp-text-large-title { font-size: var(--aisp-font-size-large-title); }

/* Apple 字重 */
.aisp-font-ultralight { font-weight: var(--aisp-font-weight-ultralight); }
.aisp-font-thin { font-weight: var(--aisp-font-weight-thin); }
.aisp-font-light { font-weight: var(--aisp-font-weight-light); }
.aisp-font-regular { font-weight: var(--aisp-font-weight-regular); }
.aisp-font-medium { font-weight: var(--aisp-font-weight-medium); }
.aisp-font-semibold { font-weight: var(--aisp-font-weight-semibold); }
.aisp-font-bold { font-weight: var(--aisp-font-weight-bold); }
.aisp-font-heavy { font-weight: var(--aisp-font-weight-heavy); }
.aisp-font-black { font-weight: var(--aisp-font-weight-black); }

/* Apple 间距系统 (8pt grid) */
.aisp-m-0 { margin: 0; }
.aisp-m-1 { margin: var(--aisp-spacing-1); }
.aisp-m-2 { margin: var(--aisp-spacing-2); }
.aisp-m-3 { margin: var(--aisp-spacing-3); }
.aisp-m-4 { margin: var(--aisp-spacing-4); }
.aisp-m-5 { margin: var(--aisp-spacing-5); }
.aisp-m-6 { margin: var(--aisp-spacing-6); }
.aisp-m-7 { margin: var(--aisp-spacing-7); }
.aisp-m-8 { margin: var(--aisp-spacing-8); }

.aisp-mt-1 { margin-top: var(--aisp-spacing-1); }
.aisp-mt-2 { margin-top: var(--aisp-spacing-2); }
.aisp-mt-3 { margin-top: var(--aisp-spacing-3); }
.aisp-mt-4 { margin-top: var(--aisp-spacing-4); }
.aisp-mt-5 { margin-top: var(--aisp-spacing-5); }

.aisp-mb-1 { margin-bottom: var(--aisp-spacing-1); }
.aisp-mb-2 { margin-bottom: var(--aisp-spacing-2); }
.aisp-mb-3 { margin-bottom: var(--aisp-spacing-3); }
.aisp-mb-4 { margin-bottom: var(--aisp-spacing-4); }
.aisp-mb-5 { margin-bottom: var(--aisp-spacing-5); }

.aisp-p-1 { padding: var(--aisp-spacing-1); }
.aisp-p-2 { padding: var(--aisp-spacing-2); }
.aisp-p-3 { padding: var(--aisp-spacing-3); }
.aisp-p-4 { padding: var(--aisp-spacing-4); }
.aisp-p-5 { padding: var(--aisp-spacing-5); }

/* 显示/隐藏 */
.aisp-d-none { display: none; }
.aisp-d-block { display: block; }
.aisp-d-flex { display: flex; }
.aisp-d-inline { display: inline; }
.aisp-d-inline-block { display: inline-block; }

/* Flex布局 */
.aisp-flex-row { flex-direction: row; }
.aisp-flex-column { flex-direction: column; }
.aisp-justify-start { justify-content: flex-start; }
.aisp-justify-center { justify-content: center; }
.aisp-justify-end { justify-content: flex-end; }
.aisp-justify-between { justify-content: space-between; }
.aisp-align-start { align-items: flex-start; }
.aisp-align-center { align-items: center; }
.aisp-align-end { align-items: flex-end; }

/* Apple 圆角 */
.aisp-rounded-small { border-radius: var(--aisp-corner-radius-small); }
.aisp-rounded-medium { border-radius: var(--aisp-corner-radius-medium); }
.aisp-rounded-large { border-radius: var(--aisp-corner-radius-large); }
.aisp-rounded-extra-large { border-radius: var(--aisp-corner-radius-extra-large); }
.aisp-rounded-continuous { border-radius: var(--aisp-corner-radius-continuous); }
.aisp-rounded-full { border-radius: 50%; }

/* Apple 阴影 */
.aisp-shadow-1 { box-shadow: var(--aisp-shadow-1); }
.aisp-shadow-2 { box-shadow: var(--aisp-shadow-2); }
.aisp-shadow-3 { box-shadow: var(--aisp-shadow-3); }
.aisp-shadow-4 { box-shadow: var(--aisp-shadow-4); }
.aisp-shadow-5 { box-shadow: var(--aisp-shadow-5); }

/* Apple 毛玻璃效果 */
.aisp-blur-background {
    background: var(--aisp-blur-background);
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
}

.aisp-blur-background-prominent {
    background: var(--aisp-blur-background-prominent);
    -webkit-backdrop-filter: var(--aisp-blur-material);
    backdrop-filter: var(--aisp-blur-material);
}

/* 悬浮容器 */
.aisp-hover-container {
    position: relative;
}

/* 侧边栏适配 */
.aisp-sidebar-content {
    width: 100%;
    max-width: var(--aisp-sidebar-content-max-width);
    padding: 0 var(--aisp-sidebar-padding);
}
/* #endregion */

/* #region 响应式设计 - 侧边栏适配 */
/* 侧边栏宽度断点 */
@media (max-width: 400px) {
    :root {
        --aisp-sidebar-padding: var(--aisp-spacing-4);
        --aisp-spacing-scale: 0.8;
    }

    .aisp-btn {
        padding: var(--aisp-spacing-2) var(--aisp-spacing-4);
        font-size: var(--aisp-font-size-footnote);
    }

    .aisp-input,
    .aisp-textarea,
    .aisp-select {
        padding: var(--aisp-spacing-3);
        font-size: var(--aisp-font-size-subheadline);
    }

    .aisp-card {
        border-radius: var(--aisp-corner-radius-medium);
    }

    .aisp-card-header,
    .aisp-card-body,
    .aisp-card-footer {
        padding: var(--aisp-spacing-4);
    }
}

@media (min-width: 401px) and (max-width: 450px) {
    :root {
        --aisp-sidebar-padding: var(--aisp-spacing-5);
    }

    .aisp-btn {
        padding: var(--aisp-spacing-3) var(--aisp-spacing-5);
    }
}

@media (min-width: 451px) {
    :root {
        --aisp-sidebar-padding: var(--aisp-spacing-6);
    }
}

/* 响应式工具类 */
@media (max-width: 400px) {
    .aisp-d-xs-none { display: none; }
    .aisp-d-xs-block { display: block; }
    .aisp-d-xs-flex { display: flex; }
    .aisp-text-xs-center { text-align: center; }
    .aisp-p-xs-3 { padding: var(--aisp-spacing-3); }
    .aisp-m-xs-2 { margin: var(--aisp-spacing-2); }
}

@media (min-width: 401px) and (max-width: 450px) {
    .aisp-d-sm-none { display: none; }
    .aisp-d-sm-block { display: block; }
    .aisp-d-sm-flex { display: flex; }
}

@media (min-width: 451px) {
    .aisp-d-lg-none { display: none; }
    .aisp-d-lg-block { display: block; }
    .aisp-d-lg-flex { display: flex; }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --aisp-separator-opaque: #000000;
        --aisp-separator-non-opaque: rgba(0, 0, 0, 0.8);
    }

    .aisp-btn {
        border: 2px solid currentColor;
    }

    .aisp-input,
    .aisp-textarea,
    .aisp-select {
        border-width: 2px;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .aisp-btn:hover:not(:disabled) {
        transform: none;
    }

    .aisp-btn:active:not(:disabled) {
        transform: none;
    }
}

/* 打印样式 */
@media print {
    .aisp-btn,
    .aisp-btn-hover-reveal {
        display: none !important;
    }

    .aisp-card {
        box-shadow: none;
        border: 1px solid #000;
    }

    * {
        background: white !important;
        color: black !important;
    }
}
/* #endregion */
