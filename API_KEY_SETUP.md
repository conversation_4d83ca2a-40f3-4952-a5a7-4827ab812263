# AI Side Panel - API密钥配置指南

## 概述

AI Side Panel 现在采用硬编码的方式管理API密钥，适用于内部自用场景。这种方式简化了配置流程，无需在界面中输入API密钥，但需要在代码中直接配置。

## 🔑 API密钥配置步骤

### 1. 获取 Gemini API 密钥

1. **访问 Google AI Studio**
   - 打开 [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)
   - 使用您的 Google 账户登录

2. **创建 API 密钥**
   - 点击 "Create API Key" 按钮
   - 选择或创建一个 Google Cloud 项目
   - 等待密钥生成完成

3. **复制 API 密钥**
   - 复制生成的 API 密钥（格式：AIzaSyDxxxxxxxxxxxxxxxxxxxxxxxxxxxxx）
   - 妥善保存此密钥，稍后需要用到

### 2. 配置 API 密钥文件

1. **复制模板文件**
   ```bash
   # 在项目根目录执行
   cp src/config/api-keys.js.template src/config/api-keys.js
   ```

2. **编辑配置文件**
   - 打开 `src/config/api-keys.js` 文件
   - 找到以下行：
   ```javascript
   const GEMINI_API_KEY = 'YOUR_GEMINI_API_KEY_HERE';
   ```
   - 将 `YOUR_GEMINI_API_KEY_HERE` 替换为您的实际 API 密钥：
   ```javascript
   const GEMINI_API_KEY = 'AIzaSyDxxxxxxxxxxxxxxxxxxxxxxxxxxxxx';
   ```

3. **保存文件**
   - 保存 `api-keys.js` 文件
   - 确保文件格式正确，没有语法错误

### 3. 验证配置

1. **重新加载扩展**
   - 打开 Chrome 浏览器
   - 访问 `chrome://extensions/`
   - 找到 AI Side Panel 扩展
   - 点击刷新按钮重新加载扩展

2. **检查控制台**
   - 按 F12 打开开发者工具
   - 查看控制台输出
   - 应该看到：`✅ Gemini API密钥配置正确`

3. **测试功能**
   - 打开任意网页
   - 点击扩展图标
   - 尝试使用 AI 分析功能
   - 验证是否能正常获得分析结果

## 📁 文件结构

```
src/config/
├── api-keys.js.template    # API密钥配置模板
└── api-keys.js            # 实际的API密钥配置（需要创建）
```

## 🔒 安全注意事项

### 1. 文件安全
- **不要提交真实密钥**：`api-keys.js` 文件已添加到 `.gitignore`，不会被提交到代码仓库
- **保护文件访问**：确保只有授权人员能访问包含真实密钥的文件
- **定期备份**：保留API密钥的安全备份

### 2. 密钥管理
- **定期轮换**：建议每3-6个月更换一次API密钥
- **监控使用**：定期检查API使用量和费用
- **设置限制**：在Google Cloud Console中设置适当的配额限制

### 3. 泄露处理
如果发现API密钥可能泄露：
1. 立即在 Google AI Studio 中撤销旧密钥
2. 生成新的API密钥
3. 更新 `api-keys.js` 文件
4. 重新加载扩展
5. 检查是否有异常的API使用记录

## 🛠️ 故障排除

### 常见问题

1. **API密钥格式错误**
   ```
   错误：Gemini API 密钥格式无效
   解决：确保密钥以 AIza 开头且长度为 39 字符
   ```

2. **API密钥未配置**
   ```
   错误：请在 src/config/api-keys.js 中配置有效的 Gemini API 密钥
   解决：检查是否正确复制了模板文件并配置了密钥
   ```

3. **扩展无法加载**
   ```
   错误：扩展加载失败
   解决：检查 api-keys.js 文件语法是否正确
   ```

4. **API调用失败**
   ```
   错误：API调用返回401错误
   解决：检查API密钥是否有效，是否已启用Gemini API
   ```

### 调试步骤

1. **检查文件存在**
   ```bash
   ls -la src/config/api-keys.js
   ```

2. **验证文件内容**
   ```bash
   grep "GEMINI_API_KEY" src/config/api-keys.js
   ```

3. **检查控制台日志**
   - 打开扩展的背景页面控制台
   - 查看是否有错误信息

4. **测试API连接**
   - 在控制台中执行：
   ```javascript
   getApiKeyStatus()
   ```

## 🔄 更新API密钥

当需要更新API密钥时：

1. **生成新密钥**
   - 在 Google AI Studio 中生成新的API密钥

2. **更新配置文件**
   - 编辑 `src/config/api-keys.js`
   - 替换 `GEMINI_API_KEY` 的值

3. **重新加载扩展**
   - 在 Chrome 扩展管理页面重新加载扩展

4. **验证新密钥**
   - 测试AI分析功能是否正常工作

## 📞 技术支持

如果在配置过程中遇到问题：

1. **检查文档**：仔细阅读本配置指南
2. **查看日志**：检查浏览器控制台的错误信息
3. **验证密钥**：确认API密钥在Google AI Studio中是有效的
4. **重新配置**：尝试重新生成API密钥并配置

## 📋 配置检查清单

- [ ] 已获取有效的 Gemini API 密钥
- [ ] 已复制模板文件为 `api-keys.js`
- [ ] 已正确配置 `GEMINI_API_KEY` 变量
- [ ] 已保存配置文件
- [ ] 已重新加载 Chrome 扩展
- [ ] 控制台显示 "✅ Gemini API密钥配置正确"
- [ ] AI 分析功能正常工作
- [ ] 已将 `api-keys.js` 添加到 `.gitignore`

完成以上步骤后，AI Side Panel 就可以正常使用 Gemini 2.0 Flash 模型进行智能内容分析了。
